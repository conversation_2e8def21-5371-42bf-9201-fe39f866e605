<template>
  <div class="pwd-change">
    <span class="cursor-pointer" @click="handleChangePW">修改密码</span>
    <AModal
      v-if="data.isShowPWChangeModal"
      :visible="data.isShowPWChangeModal"
      title="修改密码"
      destroy-on-close
      class="pwd-change-modal"
      @cancel="data.isShowPWChangeModal = false"
      @ok="handleSubmitPW"
    >
      <AForm
        layout="horizontal"
        :rules="rules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
        :model="data.formState"
      >
        <AFormItem name="oldPassword" label="旧密码">
          <AInputPassword
            v-model:value="data.formState.oldPassword"
            placeholder="请输入旧密码"
          />
        </AFormItem>
        <AFormItem name="newPassword" label="新密码">
          <AInputPassword
            v-model:value="data.formState.newPassword"
            placeholder="请输入新密码"
          />
        </AFormItem>
        <AFormItem name="secnewPassword" label="确认新密码">
          <AInputPassword
            v-model:value="data.formState.secnewPassword"
            placeholder="请再次输入新密码"
          />
        </AFormItem>
      </AForm>
    </AModal>
  </div>
</template>

<script lang="ts" setup>
  const router = useRouter();
  const { $encrypt } = useNuxtApp();
  const data = reactive({
    isShowPWChangeModal: false,
    formState: {
      oldPassword: '',
      newPassword: '',
      secnewPassword: ''
    }
  });
  /**
   * 密码校验
   */
  function passwordValidator(rule: any, value: string) {
    if (value) {
      if (/^(?!\d+$)(?![a-z]+$)[A-Z\d#@!~%^&*]{8,20}$/i.test(value))
        return Promise.resolve();

      return Promise.reject(new Error('需要数字、字母组合,可以包含特殊字符，8-20位'));
    }
    return Promise.reject(new Error('密码不能为空!'));
  }

  /**
   * 密码重复校验
   */
  function repPasswordValidator(rule: any, value: string) {
    if (value === data.formState.newPassword)
      return Promise.resolve();

    return Promise.reject(new Error('两次密码不一致!'));
  }
  const rules = {
    oldPassword: { required: true, message: '请输入旧密码', trigger: 'blur' },
    newPassword: {
      required: true,
      validator: passwordValidator,
      trigger: 'change'
    },
    secnewPassword: {
      required: true,
      validator: repPasswordValidator,
      trigger: 'change'
    }
  };
  function handleChangePW() {
    data.isShowPWChangeModal = true;
  }
  async function handleSubmitPW() {
    const { newPassword, oldPassword } = data.formState;
    const rsaPwd: any = $encrypt(newPassword);
    const oldRsaPwd: any = $encrypt(oldPassword);
    try {
      const res: any = await collieApi.putV1UserClientPassword({
        oldPassword: oldRsaPwd,
        password: rsaPwd
      });
      if (res) {
        message.success('修改密码成功,请重新登录系统!');
        data.isShowPWChangeModal = false;
        const result = await collieApi.getV1Logout();
        if (result)
          router.push({ path: '/login' });
      }
      else {
        message.error('修改密码失败!');
      }
    }
    catch (error: any) {
      message.error(error.data.message || '修改密码失败!');
    }
  }
</script>

<style lang="scss">
.pwd-change-modal {
  width: 480px !important;
}
</style>
