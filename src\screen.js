(() => {
  const rootClientWidth = '__ROOT_CLIENT_WIDTH__';
  const rootClientHeight = '__ROOT_CLIENT_HEIGHT__';
  const rootFontSize = '__ROOT_FONT_SIZE__';
  const rootMinFontSize = '__ROOT_MIN_FONT_SIZE__';
  const fontSizeIsRound = '__FONT_SIZE_IS_ROUND__';
  const fontSizeScale = '__FONT_SIZE_SCALE__';

  const root = document.documentElement;
  const dw = Number(rootClientWidth) || 1920;
  const dh = Number(rootClientHeight) || 1080;
  const ds = Number(fontSizeScale) || 20;
  const isRound = fontSizeIsRound === 'true';

  const calcFontSize = () => {
    const cw = root.clientWidth;
    const ch = root.clientHeight;
    const ws = cw / dw;
    const hs = ch / dh;
    const fontSize = (ws < hs ? ws : hs) * ds;
    if (rootMinFontSize && fontSize < Number(rootMinFontSize)) {
      return `${rootMinFontSize}px`;
    }
    return `${isRound ? Math.round(fontSize) : fontSize}px`;
  };
  const resizeCallback = () => {
    try {
      window.rootFontSize = root.style.fontSize = rootFontSize || calcFontSize();
    }
    catch {
      window.addEventListener('message', (e) => {
        if (e.data.event === 'rootFontSize') {
          const fontSize = e.data.data;
          fontSize && (root.style.fontSize = fontSize);
        }
      });
      window.parent.postMessage({ event: 'init' }, '*');
    }
  };
  if (!window.rootFontSize) {
    resizeCallback();
    if (!document.addEventListener)
      return;
    window.addEventListener('resize', resizeCallback, false);
    document.addEventListener('DOMContentLoaded', resizeCallback, false);
  }
})();
