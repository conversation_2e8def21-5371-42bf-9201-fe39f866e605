/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-04-12 16:25:46
 * @LastEditTime: 2024-10-15 16:04:29
 * @LastEditors: <PERSON>.<PERSON>AO
 * @Description:
 */
import antfu from '@antfu/eslint-config';

export default antfu({
  ignores: ['public', 'public/**', 'playground/public'],
  formatters: {
    /**
     * Format CSS, LESS, SCSS files, also the `<style>` blocks in Vue
     */
    css: true,
    html: true,
    markdown: 'prettier',
    // css html md等不能被eslint规则格式化的，使用prettier进行格式化
    prettierOptions: {
      printWidth: 120,
      tabWidth: 2,
      useTabs: false,
      semi: true,
      singleQuote: true,
      jsxSingleQuote: false,
      trailingComma: 'none',
      bracketSpacing: true,
      bracketSameLine: false,
      jsxBracketSameLine: false,
      proseWrap: 'never',
      arrowParens: 'always',
      htmlWhitespaceSensitivity: 'strict',
      endOfLine: 'lf',
      quoteProps: 'as-needed',
      // formatters尚未能够支持该配置 https://github.com/antfu/eslint-processor-vue-blocks/issues/4
      vueIndentScriptAndStyle: true,
      singleAttributePerLine: false
    }
  },
  vue: true,
  typescript: true
}, {
  // Without `files`, they are general rules for all files
  // 手动修改的规则
  rules: {
    'style/semi': ['error', 'always'], // 要求使用分号代替 ASI
    'style/comma-dangle': ['error', 'never'], // 禁止在语句末尾使用多余的逗号
    'no-console': 'off', // 允许使用console
    'unused-imports/no-unused-vars': 'warn', // 警告未使用的变量
    'no-fallthrough': ['error', { allowEmptyCase: true }], // 允许空case
    'ts/no-unused-expressions': 'off' // 禁止无用的表达式
  }
}, {
  files: ['**/*.vue'],
  rules: {
    'style/indent': 'off', // 禁用原始eslint缩进规则
    'style/indent-binary-ops': 'off', // 禁用二进制运算符缩进规则
    'vue/no-unused-vars': 'warn', // 警告未使用的变量
    'vue/block-order': [
      'error',
      {
        order: [
          'template',
          'script:not([setup])',
          'script[setup]',
          'style:not([scoped])',
          'style[scoped]'
        ]
      }
    ], // 控制vue单文件组件模板和脚本的顺序
    'vue/script-indent': [
      'error',
      2,
      {
        baseIndent: 1
      }
    ] // vue单文件script标签缩进
  }
});
