<!--
 * @Author: <PERSON>tz
 * @Date: 2024-02-27 11:29:32
 * @LastEditTime: 2025-03-17 20:44:31
 * @LastEditors: G.TAO
 * @Description: 全局错误页面
-->
<template>
  <AppProvider :theme="themeMap[theme]">
    <div class="flex items-center justify-center w-full h-full text-xxl">
      <AResult :status="errorInfo.status" :title="errorInfo.title" :sub-title="errorInfo.msg">
        <template #extra>
          <div class="flex flex-col items-center gap-xl">
            <div class="text-content-secondary text-opacity-60">
              {{ message }}
            </div>
            <div class="flex items-center gap-xl">
              <AButton type="primary" @click="toIndex">
                回到首页
              </AButton>
              <AButton type="primary" @click="toLogin">
                {{ auth?.user ? '切换账号' : '登陆' }}
              </AButton>
              <AButton v-if="errorInfo.title === '402'" type="primary" @click="toLicense">
                授权
              </AButton>
            </div>
          </div>
        </template>
      </AResult>
    </div>
  </AppProvider>
</template>

<script setup lang="ts">
  const props: any = defineProps({
    error: {
      type: Object,
      default: () => ({})
    }
  });
  const theme = useTheme();
  const auth = useAuth();
  const runtimeConfig = useRuntimeConfig();
  const { baseURL } = runtimeConfig.app;

  const themeMap = ref<any>({});
  try {
    const configData: any = await appApi.getAppConfig();
    useSeoMeta({
      title: configData?.name
    });
    themeMap.value = (configData?.theme || {}) as IThemeConfig;
  }
  catch (error) {
    console.log(error);
  }
  console.log(props.error);
  const { statusCode, message } = props.error;
  const errorMap: any = {
    500: {
      status: 500,
      title: '500',
      msg: `对不起，网络故障或服务器发生错误`
    },
    404: {
      status: 404,
      title: '404',
      msg: '对不起，您访问的页面不存在'
    },
    401: {
      status: 403,
      title: '401',
      msg: '对不起，您没有访问此页面的权限'
    },
    402: {
      status: 403,
      title: '402',
      msg: '对不起，您没访问的系统未授权'
    }
  };

  const errorInfo = computed(() => {
    return errorMap[statusCode] || errorMap[500];
  });

  function toIndex() {
    navigateTo(baseURL, { external: true });
  }

  function toLogin() {
    navigateTo(`${baseURL}login?redirect=${window.location.href}`, { external: true });
  }

  function toLicense() {
    navigateTo(`${baseURL}license`, { external: true });
  }
</script>

<style lang="scss" scoped>
  :deep(.ant-result) {
  .ant-result-image {
    @apply w-[252px] h-[294px];
  }
}
</style>
