import hjson from 'hjson';

const { get } = useRequest();

async function getAppConfig() {
  const runtimeConfig = useRuntimeConfig();
  const { appMode } = runtimeConfig.public;
  const configUrl = appMode === 'ssg' ? '/config/app.hjson' : '/api/config';
  const appConfig: any = await get('', configUrl);
  if (appConfig instanceof Blob) {
    const text = await appConfig.text();
    return hjson.parse(text);
  }
  else if (typeof appConfig === 'string') {
    return hjson.parse(appConfig);
  }
  else {
    return appConfig;
  }
};

export const appApi = {
  getAppConfig
};
