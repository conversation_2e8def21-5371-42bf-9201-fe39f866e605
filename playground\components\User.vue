<template>
  <div class="user">
    <div v-if="!auth?.user" class="flex items-center gap-base cursor-pointer" @click="handleLogin">
      <SvgIcon name="login" />
      <span class="text-xl">登录</span>
    </div>
    <ADropdown v-else placement="bottomRight">
      <div class="flex items-center justify-center gap-base cursor-pointer">
        <SvgIcon name="avatar" class="avatar-icon rounded-full shadow text-transparent" />
        <span class="font-bold">{{ auth.user.userName }}</span>
      </div>
      <template #overlay>
        <AMenu>
          <AMenuItem v-if="auth.user.userSystemPlatform === 'collie'">
            <PwdChange />
          </AMenuItem>
          <AMenuItem @click="handleLogout">
            退出登录
          </AMenuItem>
        </AMenu>
      </template>
    </ADropdown>
  </div>
</template>

<script lang="ts" setup>
  const auth = useAuth();
  function handleLogin() {
    navigateTo('/login');
  }
  async function handleLogout() {
    const result = await collieApi.getV1Logout();
    if (result)
      navigateTo('/login');
  }
</script>

<style lang="scss" scoped>
.user {
  .avatar-icon {
    width: 40px;
    height: 40px;
  }
}
</style>
