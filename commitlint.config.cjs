/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-03-05 17:19:38
 * @LastEditTime: 2024-03-05 18:17:14
 * @LastEditors: Hertz
 * @Description:
 */

const typeEnum = require('./.cz-config.cjs');

module.exports = {
  extends: ['@commitlint/config-conventional'],
  rules: {
    'body-leading-blank': [1, 'always'], // body开始于空白行
    'footer-leading-blank': [1, 'always'], // footer开始于空白行
    'type-enum': [2, 'always', typeEnum.types.map(i => i.value)]
  }
};
