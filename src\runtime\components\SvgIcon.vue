<template>
  <div :class="[`svg-icon-${name}`]" class="svg-icon inline-block">
    <component :is="value" v-if="value" :fill="fill" :stroke="stroke" aria-hidden="true" :transform="`rotate(${rotate})`" class="w-full h-full" />
    <ClientOnly v-else>
      <!-- eslint-disable-next-line vue/html-self-closing -->
      <svg
        v-if="SVGConfig"
        aria-hidden="true"
        class="w-full h-full"
        :fill="SVGConfig.fill || fill"
        :stroke="SVGConfig.stroke || stroke"
        :stroke-width="0"
        :transform="SVGConfig.rotate"
        stroke-linejoin="round"
        :viewBox="SVGConfig.viewBox"
        preserveAspectRatio="xMidYMid meet"
        xmlns="http://www.w3.org/2000/svg"
        v-html="SVGConfig.content"
      ></svg>
      <svg v-else aria-hidden="true" :fill="fill" :stroke="stroke" :transform="`rotate(${rotate})`" class="w-full h-full">
        <use :xlink:href="iconName" />
      </svg>
    </ClientOnly>
  </div>
</template>

<script lang="ts" setup>
  import type { WatchStopHandle } from 'vue';
  import { computed, ref, watch } from 'vue';

  interface ISvgIcon {
    fill: string
    stroke: string
    viewBox: string
    content: string
    rotate: string | undefined
  }

  const props = defineProps({
    name: {
      type: String,
      required: true
    },
    value: {
      type: [Object, String]
    },
    rotate: {
      type: Number,
      default: 0
    },
    stroke: {
      type: String,
      default: 'currentcolor'
    },
    fill: {
      type: String,
      default: 'currentcolor'
    }
  });

  let waitSvg: WatchStopHandle | null;
  const SVGConfig = ref<ISvgIcon | null>(null);

  function getSVGConfig() {
    if (!process.client)
      return;
    /*
      当应用为非微前端方式加载或非微前端子应用时, window.proxy为空, 读取全局属性rainbowIcons
      当应用为微前端子应用时, window.proxy.rainbowIcons会代理到主应用图标库或者是读取到子应用独立图标库
    */
    const rainbowIcons = (window as any).proxy?.rainbowIcons || (window as any).rainbowIcons || {};
    SVGConfig.value = rainbowIcons[props.name] || null;

    if (SVGConfig.value && props.rotate)
      SVGConfig.value.rotate = `rotate(${props.rotate})`;

    if (!SVGConfig.value) {
      // svg未加载, 监听全局属性
      waitSvg = watch(
        rainbowIcons,
        () => {
          getSVGConfig();
          if (SVGConfig.value && waitSvg) {
            // 清除监听
            waitSvg();
            waitSvg = null;
          }
        },
        {
          deep: true
        }
      );
    }
  }

  watch(
    () => props.name,
    () => {
      if (waitSvg) {
        // 清除历史监听
        waitSvg();
        waitSvg = null;
      }
      getSVGConfig();
    },
    {
      immediate: true
    }
  );

  const iconName = computed(() => {
    return `#icon-${props.name}`;
  });
</script>

<style lang="scss" scoped>
.svg-icon {
  width: 1rem;
  height: 1rem;
}
</style>
