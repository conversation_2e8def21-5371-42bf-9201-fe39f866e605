<template>
  <div class="left-navbar h-full overflow-y-auto">
    <AMenu
      v-model:selected-keys="data.selectedKeys"
      v-model:open-keys="data.openKeys"
      mode="inline"
      class="menu-wrapper"
      @select="handleMenuItemSelect"
    >
      <ClientOnly>
        <template v-for="item in menuList" :key="item.url">
          <template v-if="item.permCodes.includes('visible')">
            <template v-if="item.children && item.children.length > 0">
              <ASubMenu :key="item.url">
                <template #icon>
                  <SvgIcon class="w-2xl h-2xl" :name="item.icon || 'collect'" />
                </template>
                <template #expandIcon="{ isOpen }">
                  <SvgIcon class="w-2xl h-2xl" :name="isOpen ? 'chevron-up' : 'chevron-down'" />
                </template>
                <template #title>
                  {{ item.name }}
                </template>
                <template v-for="secondItem in item.children" :key="secondItem.url">
                  <template v-if="secondItem.permCodes.includes('visible')">
                    <template v-if="secondItem.children && secondItem.children.length > 0">
                      <ASubMenu :key="secondItem.url">
                        <template #title>
                          <span class="ml-xl pl-sm">{{ secondItem.name }}</span>
                        </template>
                        <template #expandIcon="{ isOpen }">
                          <SvgIcon
                            class="w-2xl h-2xl"
                            :name="isOpen ? 'chevron-up' : 'chevron-down'"
                          />
                        </template>
                        <template v-for="thirdItem in secondItem.children" :key="thirdItem.url">
                          <template v-if="thirdItem.permCodes.includes('visible')">
                            <AMenuItem
                              :key="thirdItem.url"
                              :disabled="!thirdItem.permCodes.includes('usable')"
                            >
                              <span class="ml-2xl pl-sm">{{ thirdItem.name }}</span>
                            </AMenuItem>
                          </template>
                        </template>
                      </ASubMenu>
                    </template>
                    <template v-else>
                      <AMenuItem
                        :key="secondItem.url"
                        :disabled="!secondItem.permCodes.includes('usable')"
                      >
                        <span class="ml-xl pl-sm">{{ secondItem.name }}</span>
                      </AMenuItem>
                    </template>
                  </template>
                </template>
              </ASubMenu>
            </template>
            <template v-else>
              <AMenuItem :key="item.url" :disabled="!item.permCodes.includes('usable')">
                <template #icon>
                  <SvgIcon class="w-2xl h-2xl" :name="item.icon || 'collect'" />
                </template>
                {{ item.name }}
              </AMenuItem>
            </template>
          </template>
        </template>
      </ClientOnly>
    </AMenu>
  </div>
</template>

<script lang="ts" setup>
  const props = defineProps({
    // 左侧菜单栏，目前最多支持三级菜单
    menuList: {
      type: Array as PropType<any[]>,
      required: true
    }
  });
  const route = useRoute();
  const data = reactive({
    selectedKeys: [] as any[],
    openKeys: [] as any[]
  });

  function findNodeParents(nodes: IResource[], path: string, parentPaths: string[] = []): string[] {
    for (const node of nodes) {
      if (node.url === path)
        return parentPaths;

      if (node.children && node.children.length) {
        const result: string[] = findNodeParents(node.children, path, [...parentPaths, node.url]);
        if (result && result.length)
          return result;
      }
    }
    return [];
  }

  watchEffect(() => {
    const path = utils.formatPagePath(route.fullPath);
    const nodePaths: string[] = findNodeParents(props.menuList, path);

    data.selectedKeys = [...nodePaths, path];
    data.openKeys = [...data.openKeys, ...nodePaths];
  });
  function gotoRoute(value: IResource) {
    const fullPath = utils.formatPagePath(route.fullPath);
    const key: string = value.url;
    if (value.children.length > 0) {
      const index = data.selectedKeys.indexOf(key);
      if (index > -1)
        data.selectedKeys.splice(index, 1);
      else
        data.selectedKeys.push(key);
    }
    else if (/https?:\/\//.test(key)) {
      window.open(key, '_blank');
    }
    else if (fullPath !== key) {
      navigateTo(key);
    }
  }

  function getAppResourceByPath(array: any[], paths: string[]): any {
    const result: any = array.find((item: IResource) => {
      return item.url === paths[0];
    });
    if (paths.length > 1)
      return getAppResourceByPath(result.children, paths.slice(1));

    return result;
  }

  function handleMenuItemSelect({ keyPath }: any) {
    const item: any = getAppResourceByPath(props.menuList, keyPath);
    if (item)
      gotoRoute(item);
  }
  onMounted(() => {});
</script>

<style lang="scss" scoped>
.left-navbar {
  .menu-wrapper {
    @apply w-full h-full py-2xl px-base;
  }

  :deep(.ant-menu-item) {
    height: 50px !important;
    line-height: 50px !important;
    @apply px-xl #{!important};
  }
  :deep(.ant-menu-submenu-title) {
    height: 50px !important;
    line-height: 50px !important;
    @apply px-xl #{!important};
  }

  :deep(.ant-menu-item-selected) {
    @apply bg-primary text-white rounded #{!important};
  }
  :deep(.ant-menu-sub.ant-menu-inline) {
    background: inherit !important;
  }
}
</style>
