import type { ModuleOptions } from './types';
import { promises as fsp } from 'node:fs';
import { addComponentsDir, addImportsDir, addPlugin, addServerScanDir, createResolver, defineNuxtModule, extendPages, installModule, resolveAlias } from '@nuxt/kit';
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons';
import svgLoader from 'vite-svg-loader';
import { name, version } from '../package.json';

const plugins: string[] = ['svg.client', 'microApp.client', 'encrypt.client'];
const pages: string[] = ['welcome'];

const defaults: ModuleOptions = {
  ignorePlugins: []
};

export default defineNuxtModule<Partial<ModuleOptions>>({
  meta: {
    name,
    version,
    configKey: 'tys'
  },
  defaults,
  async setup(_options, nuxt) {
    const options = _options as ModuleOptions;

    const { resolve } = createResolver(import.meta.url);
    const runtimeDir = await resolve('./runtime');

    const scriptPath = await resolve('./screen.js');
    const script = await fsp.readFile(scriptPath, 'utf-8');

    nuxt.hook('nitro:config', (config) => {
      config.externals = config.externals || {};
      config.externals.inline = config.externals.inline || [];
      config.externals.inline.push(runtimeDir);
      config.virtual = config.virtual || {};
      config.virtual['#screen-script'] = `export const script = ${JSON.stringify(script, null, 2)}`;
      config.plugins = config.plugins || [];
      config.plugins.push(resolve(`./runtime/plugins/nitro-plugin`));
    });

    Object.assign(nuxt.options, { antd: {
      extractStyle: true
    } });
    await installModule('@nuxtjs/tailwindcss');
    await installModule('@vueuse/nuxt');
    await installModule('@nuxt/image');
    await installModule('@ant-design-vue/nuxt');

    addImportsDir(resolve('./runtime/composables'));

    addComponentsDir({
      path: resolve('./runtime/components')
    });

    plugins.forEach((plugin: any) => {
      if (!options.ignorePlugins.includes(plugin))
        addPlugin(resolve(`./runtime/plugins/${plugin}`));
      ;
    });

    pages.forEach((pagePath: string) => {
      extendPages((pages) => {
        pages.unshift({
          path: `/${pagePath}`,
          file: resolve(`./runtime/pages/${pagePath}/index.vue`)
        });
      });
    });

    addServerScanDir(resolve('./runtime/server'));

    nuxt.options.postcss = nuxt.options.postcss || {};
    nuxt.options.postcss.plugins = {
      'postcss-pxtorem': {
        rootValue: 20,
        minPixelValue: 2,
        propList: ['*'],
        unitPrecision: 2,
        selectorBlackList: ['ant-result-image']
      },
      ...(nuxt.options.postcss.plugins || [])
    };

    nuxt.hook('vite:extendConfig', (config) => {
      config.resolve = config.resolve || {};
      config.resolve.alias = {
        'ant-design-vue/dist': 'ant-design-vue/dist',
        'ant-design-vue/es': 'ant-design-vue/es',
        'ant-design-vue/lib': 'ant-design-vue/es',
        'ant-design-vue': 'ant-design-vue/es',
        ...(config.resolve.alias || {})
      };

      config.optimizeDeps = config.optimizeDeps || {};
      config.optimizeDeps.include = [
        '@babel/runtime/regenerator',
        'lodash',
        'lodash-es',
        'dayjs',
        'dayjs/locale/zh-cn',
        ...(config.optimizeDeps.include || [])
      ];

      config.plugins = config.plugins || [];
      const inputDir = resolveAlias('~/assets/svgs', nuxt.options.alias);
      config.plugins = [createSvgIconsPlugin({
        iconDirs: [inputDir],
        symbolId: 'icon-[name]'
      }), svgLoader(), ...(config.plugins || [])];
    });
  }
});
