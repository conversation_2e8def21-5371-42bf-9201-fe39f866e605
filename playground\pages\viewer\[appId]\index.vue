<template>
  <div id="viewer" class="relative w-full h-full" />
</template>

<script lang="ts" setup>
  import widgetModules from '../../../widgets';

  const route = useRoute();
  const { registerWidget, openWidget } = useWidget();
  const { appId } = route.params;

  const result: any = await syskeeperApi.getV1AuthorizationMyAppletIdComponent(appId as string);
  const widgets = registerWidget(widgetModules, result || []);
  onMounted(() => {
    widgets
      && widgets.forEach((item: any) => {
        openWidget(item);
      });
  });
</script>
