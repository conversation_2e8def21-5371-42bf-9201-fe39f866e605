<!--
 * @Author: <PERSON>tz
 * @Date: 2024-04-12 16:00:28
 * @LastEditTime: 2025-03-12 15:52:59
 * @LastEditors: Hertz
 * @Description:
-->
<template>
  <ClientOnly>
    <MicroApp v-if="data.appInfo" :app="data.appInfo" />
  </ClientOnly>
</template>

<script lang="ts" setup>
  definePageMeta({
    middleware: 'auth'
  });
  const route = useRoute();
  const config = useConfig();

  interface IData {
    appInfo: null | { name: string, url: string } | any
  }

  const data = reactive<IData>({
    appInfo: null
  });

  function loadApp() {
    const { apps } = config.value as IConfig;
    apps?.forEach((item: IApp) => {
      if (route.query[item.name]) {
        const appRoute = route.query[item.name]?.replace(item.path, '') as string;
        item.route = appRoute.startsWith('/') ? appRoute : `/${appRoute}`;
        data.appInfo = item;
      }
    });
  }
  watch(
    () => route.fullPath,
    (newVar, oldVar) => {
      if (import.meta.client && (utils.formatPagePath(newVar || '') !== utils.formatPagePath(oldVar || '')))
        loadApp();
    },
    { immediate: true }
  );
</script>

<style lang="scss" scoped></style>
