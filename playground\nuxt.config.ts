/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-02-26 13:28:35
 * @LastEditTime: 2025-03-27 20:49:22
 * @LastEditors: <PERSON><PERSON><PERSON>AO
 * @Description:
 */
// https://nuxt.com/docs/api/configuration/nuxt-config
import process from 'node:process';

const { env } = process;

export default defineNuxtConfig({
  devtools: { enabled: false },
  app: {
    baseURL: env.NUXT_APP_BASE_URL,
    head: {}
  },
  css: ['@/assets/styles/index.scss'],
  alias: {
    '@tys/nuxt': '../dist/runtime/index'
  },
  modules: [
    '../dist/module'
  ],
  tys: {
    ignorePlugins: []
  },
  vite: {
    base: env.NUXT_APP_BASE_URL,
    css: {
      preprocessorOptions: {
        scss: {
          api: 'modern-compiler'
        }
      }
    }
  },
  devServer: {
    host: env.NUXT_APP_HOST,
    port: Number(env.NUXT_APP_PORT)
  },
  nitro: {
    devProxy: {
      '/collie/': {
        target: 'http://*************:10200/collie/',
        changeOrigin: true
      },
      '/main/': {
        target: 'http://*************:10200/main/',
        changeOrigin: true
      },
      '/syskeeper/': {
        target: 'http://*************:10100/syskeeper/',
        changeOrigin: true
      },
      '/mask/': {
        target: 'http://*************:10100/mask/',
        changeOrigin: true
      },
      '/freeflow/': {
        target: 'http://*************:10100/freeflow/',
        changeOrigin: true
      },
      '/chaos/': {
        target: 'http://*************:10100/chaos/',
        changeOrigin: true
      },
      '/facemap/': {
        target: 'http://*************:10100/facemap/',
        changeOrigin: true
      },
      // '/dimenlink-home/': {
      //   target: 'http://*************:10100/dimenlink-home/',
      //   changeOrigin: true
      // },
      '/plotchain-meta/': {
        target: 'http://*************:10100/plotchain-meta/',
        changeOrigin: true
      },
      '/dimenlink/': {
        target: 'http://192.168.1.190:10114/dimenlink/',
        changeOrigin: true
      }
    }
  },
  vue: {
    compilerOptions: {
      isCustomElement: tag => tag.startsWith('micro-app')
    }
  },
  compatibilityDate: '2024-10-15'
});
