/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-02-27 13:30:10
 * @LastEditTime: 2025-06-27 13:20:24
 * @LastEditors: Lauxb
 * @Description:
 */
import CryptoJS from 'crypto-js';

// 全局默认 aes 密钥
const AES_KEY = 'tysxdata20140222';
const IV_DATA = [
  0x13,
  0x16,
  0x19,
  0x22,
  0x24,
  0x27,
  0x30,
  0x33,
  0x35,
  0x39,
  0x41,
  0x44,
  0x48,
  0x52,
  0x55,
  0x57
];
const IV_DATA_STRING = IV_DATA.map(item => String.fromCharCode(item)).join('');

/**
 * AES CBC加密
 * @param word 加密文本或对象
 * @param encode 编码类型，默认使用string,可选 string/hex/base64
 * @param key 密钥
 * @param iv 偏移量
 * @returns
 */
function Encrypt(word: string | object, encode: 'string' | 'hex' | 'base64' = 'string', key: string = AES_KEY, iv: string = IV_DATA_STRING) {
  try {
    const parsedKey = CryptoJS.enc.Utf8.parse(key);
    const parsedIV = CryptoJS.enc.Utf8.parse(iv);
    const contentString = typeof word === 'string' ? word : JSON.stringify(word);
    const parsedContent = CryptoJS.enc.Utf8.parse(contentString);
    const encrypted = CryptoJS.AES.encrypt(parsedContent, parsedKey, {
      iv: parsedIV,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    });
    switch (encode) {
      case 'hex':
        return encrypted.ciphertext.toString(CryptoJS.enc.Hex);
      case 'base64':
        return encrypted.ciphertext.toString(CryptoJS.enc.Base64);
      default:
        return encrypted.ciphertext.toString();
    }
  }
  catch (error: any) {
    console.error('加密失败:', error);
    throw new Error('数据加密失败');
  }
}

/**
 * AES CBC解密
 * @param {*} word 加密后的文本
 * @param {*} encode 加密文本编码类型，默认使用string,可选 string/hex/base64
 * @param {*} key 解密密钥
 * @param {*} iv 偏移量
 * @returns string
 */
function Decrypt(word: string, encode: 'string' | 'hex' | 'base64' = 'string', key: string = AES_KEY, iv: string = IV_DATA_STRING) {
  try {
    const aesKey = CryptoJS.enc.Utf8.parse(key);
    const aesIv = CryptoJS.enc.Utf8.parse(iv);
    let aesContent: any;
    switch (encode) {
      case 'hex':
        aesContent = CryptoJS.enc.Hex.parse(word).toString();
        break;
      case 'base64':
        aesContent = CryptoJS.enc.Base64.parse(word).toString();
        break;
      default:
        aesContent = word;
    }

    const decrypt = CryptoJS.AES.decrypt(aesContent, aesKey, {
      iv: aesIv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    });
    return decrypt.toString(CryptoJS.enc.Utf8);
  }
  catch (error: any) {
    console.error('解密失败:', error);
    throw new Error('数据解密失败');
  }
}

export default {
  Encrypt,
  Decrypt
};
