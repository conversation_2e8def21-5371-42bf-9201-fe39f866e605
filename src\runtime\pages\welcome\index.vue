<!--
 * @Author: <PERSON><PERSON>
 * @Date: 2024-02-27 17:37:15
 * @LastEditTime: 2024-05-10 10:51:15
 * @LastEditors: G<PERSON><PERSON>AO
 * @Description:
-->
<template>
  <div class="welcome flex flex-col items-center gap-3xl px-5xl py-xl h-full overflow-y-auto">
    <div class="flex flex-col items-center gap-2xl">
      <img src="/image/logo.png" class="h-5xl" alt="logo">
      <h1 class="text-7xl font-bold text-center">
        欢迎使用图源素罐子云服务端渲染应用框架
      </h1>
    </div>
    <ARadioGroup v-model:value="theme" button-style="solid">
      <ARadioButton
        v-for="(key, index) in Object.keys(config?.theme || {})"
        :key="index"
        :value="key"
      >
        {{ config?.theme[key]?.label }}
      </ARadioButton>
    </ARadioGroup>
    <ACard title="子应用场景" class="w-full">
      <template #extra>
        <div class="flex items-center gap-xl">
          <ASelect
            v-model:value="loadFrame"
            class="w-5xl"
            @change="
              () => {
                isDegrade = false;
              }
            "
          >
            <ASelectOption value="microApp">
              microApp
            </ASelectOption>
            <ASelectOption value="wujie">
              wujie
            </ASelectOption>
          </ASelect>
          <AInput
            v-model:value="appUrl"
            placeholder="应用地址，必须支持跨域"
            class="app-url-input"
          />
          <AInput v-model:value="appPath" placeholder="路由路径, 以/开始" class="app-path-input" />
          <ACheckbox v-if="loadFrame === 'wujie'" v-model:checked="isDegrade" class="flex-shrink-0">
            是否降级加载
          </ACheckbox>
          <AButton type="primary" @click="handleLoadApp">
            加载应用
          </AButton>
        </div>
      </template>
      <div class="sub-app overflow-y-auto">
        <MicroApp v-if="loadApp" :app="loadApp" />
      </div>
    </ACard>
  </div>
</template>

<script lang="ts" setup>
  import type { IApp } from '../../composables/config';
  import { ref } from 'vue';
  import MicroApp from '../../components/MicroApp.vue';
  import { useConfig } from '../../composables/config';
  import { useTheme } from '../../composables/theme';

  const config = useConfig();
  const theme = useTheme();

  const loadFrame = ref('microApp');
  const appUrl = ref('');
  const isDegrade = ref(false);
  const appPath = ref('');
  const loadApp = ref();

  function handleLoadApp() {
    if (!appUrl.value)
      loadApp.value = null;

    const appInfo: IApp = {
      label: '子应用',
      name: `${appUrl.value}${loadFrame.value}`,
      frame: loadFrame.value,
      options: { degrade: isDegrade.value }
    };
    appInfo.url = appUrl.value;
    appInfo.route = appPath.value || '';

    loadApp.value = appInfo;
  }
</script>

<style scoped>
.welcome .sub-app {
  height: 600px;
}
.welcome .app-url-input {
  width: 400px;
}
.welcome .app-path-input {
  width: 300px;
}
</style>
