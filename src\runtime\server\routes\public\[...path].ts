import fs from 'node:fs';
import process from 'node:process';
import { defineE<PERSON><PERSON><PERSON><PERSON>, getRouterParam, sendStream } from 'h3';

export default defineEventHandler((event) => {
  const filePath = getRouterParam(event, 'path');
  const envMap = process.env;
  let rootPath = '.';
  if (envMap.NUXT_VITE_NODE_OPTIONS) {
    const nuxtViteNodeOptions = JSON.parse(envMap.NUXT_VITE_NODE_OPTIONS);
    rootPath = nuxtViteNodeOptions.root;
  }
  return sendStream(event, fs.createReadStream(`${rootPath}/public/${filePath}`));
});
