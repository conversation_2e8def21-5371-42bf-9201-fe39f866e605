const apiName = 'collie';

const { get, post, put, del } = useRequest();

function getHeaders() {
  const cookies = getCookies();
  if (cookies)
    return { Cookie: cookies };
  return {} as any;
};

/**
 * @description 获取应用api列表
 * @param params 参数 {
 *   appId： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1Api(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/api', { headers: getHeaders(), ...options, params });
}

/**
 * @description 更新api
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function putV1Api(body: Record<string, any>, options: OptionsType = {}) {
  return put(apiName, '/v1/api', { body, headers: getHeaders(), ...options });
}

/**
 * @description 注册api
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1Api(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/api', { body, headers: getHeaders(), ...options });
}

/**
 * @description 批量删除api
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function deleteV1Api(body: Record<string, any>, options: OptionsType = {}) {
  return del(apiName, '/v1/api', { body, headers: getHeaders(), ...options });
}

/**
 * @description 修改第三方应用api请求控制
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function putV1ApiAppPermission(body: Record<string, any>, options: OptionsType = {}) {
  return put(apiName, '/v1/api/app/permission', { body, headers: getHeaders(), ...options });
}

/**
 * @description 新增第三方应用api请求控制
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1ApiAppPermission(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/api/app/permission', { body, headers: getHeaders(), ...options });
}

/**
 * @description 批量删除第三方api授权
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function deleteV1ApiAppPermission(body: Record<string, any>, options: OptionsType = {}) {
  return del(apiName, '/v1/api/app/permission', { body, headers: getHeaders(), ...options });
}

/**
 * @description 批量上线/下线第三方app api
 * @param params 参数 {
 *   enable： boolean
 * }
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function putV1ApiAppPermissionEnable(params: Record<string, any>, body: Record<string, any>, options: OptionsType = {}) {
  return put(apiName, '/v1/api/app/permission/enable', { body, headers: getHeaders(), ...options, params });
}

/**
 * @description 获取第三方应用拥有授权的api列表
 * @param index string
 * @param size string
 * @param params 参数 {
 *   appId： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1ApiAppPermissionPageIndexSize(index: string, size: string, params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, `/v1/api/app/permission/page/${index}/${size}`, { headers: getHeaders(), ...options, params });
}

/**
 * @description 获取第三方应用api请求控制详情
 * @param id string
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1ApiAppPermissionId(id: string, options: OptionsType = {}) {
  return get(apiName, `/v1/api/app/permission/${id}`, { headers: getHeaders(), ...options });
}

/**
 * @description 获取角色api权限
 * @param params 参数 {
 *   roleId： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1ApiAuthorizationPermission(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/api/authorization/permission', { headers: getHeaders(), ...options, params });
}

/**
 * @description 修改角色api权限
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1ApiAuthorizationPermission(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/api/authorization/permission', { body, headers: getHeaders(), ...options });
}

/**
 * @description 批量上线/下线api
 * @param params 参数 {
 *   enable： boolean
 * }
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function putV1ApiEnable(params: Record<string, any>, body: Record<string, any>, options: OptionsType = {}) {
  return put(apiName, '/v1/api/enable', { body, headers: getHeaders(), ...options, params });
}

/**
 * @description 获取网关代理app
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1ApiListApp(options: OptionsType = {}) {
  return get(apiName, '/v1/api/list/app', { headers: getHeaders(), ...options });
}

/**
 * @description 获取app接口tag分组
 * @param params 参数 {
 *   appId： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1ApiListTag(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/api/list/tag', { headers: getHeaders(), ...options, params });
}

/**
 * @description 获取流量限制单位
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1ApiTimeunit(options: OptionsType = {}) {
  return get(apiName, '/v1/api/timeunit', { headers: getHeaders(), ...options });
}

/**
 * @description 修改应用
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function putV1App(body: Record<string, any>, options: OptionsType = {}) {
  return put(apiName, '/v1/app', { body, headers: getHeaders(), ...options });
}

/**
 * @description 添加应用
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1App(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/app', { body, headers: getHeaders(), ...options });
}

/**
 * @description 获取应用列表
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1AppList(options: OptionsType = {}) {
  return get(apiName, '/v1/app/list', { headers: getHeaders(), ...options });
}

/**
 * @description 获取应用详情
 * @param id string
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1AppId(id: string, options: OptionsType = {}) {
  return get(apiName, `/v1/app/${id}`, { headers: getHeaders(), ...options });
}

/**
 * @description 禁用/启用应用
 * @param id string
 * @param params 参数 {
 *   enable： boolean
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function putV1AppId(id: string, params: Record<string, any>, options: OptionsType = {}) {
  return put(apiName, `/v1/app/${id}`, { headers: getHeaders(), ...options, params });
}

/**
 * @description 删除应用
 * @param id string
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function deleteV1AppId(id: string, options: OptionsType = {}) {
  return del(apiName, `/v1/app/${id}`, { headers: getHeaders(), ...options });
}

/**
 * @description 获取接口认证token
 * @param params 参数 {
 *   validTime?： string
 *   userId?： string
 *   strict?： boolean
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1AuthorizationToken(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/authorization/token', { headers: getHeaders(), ...options, params });
}

/**
 * @description 数据解密
 * @param params 参数 {
 *   encryptType： string
 *   key?： string
 *   data： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1Decrypt(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/decrypt', { headers: getHeaders(), ...options, params });
}

/**
 * @description 通过浙政钉authCode进行认证
 * @param authCode string
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1DingtalkSsoAuthCode(authCode: string, options: OptionsType = {}) {
  return get(apiName, `/v1/dingtalk/sso/${authCode}`, { headers: getHeaders(), ...options });
}

/**
 * @description 数据加密
 * @param params 参数 {
 *   encryptType： string
 *   key?： string
 *   data： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1Encrypt(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/encrypt', { headers: getHeaders(), ...options, params });
}

/**
 * @description 登出
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1Logout(options: OptionsType = {}) {
  return get(apiName, '/v1/logout', { headers: getHeaders(), ...options });
}

/**
 * @description 宁波市域平台单点登录认证
 * @param params 参数 {
 *   token： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1NbsyptSso(params: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/nbsypt/sso', { headers: getHeaders(), ...options, params });
}

/**
 * @description sso验证
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1Sso(options: OptionsType = {}) {
  return get(apiName, '/v1/sso', { headers: getHeaders(), ...options });
}

/**
 * @description sso验证
 * @param token string
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1SsoToken(token: string, options: OptionsType = {}) {
  return get(apiName, `/v1/sso/${token}`, { headers: getHeaders(), ...options });
}

/**
 * @description 获取凭证
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1Ticket(options: OptionsType = {}) {
  return get(apiName, '/v1/ticket', { headers: getHeaders(), ...options });
}

/**
 * @description 根据ticket获取用户信息
 * @param params 参数 {
 *   ticket： string
 *   detail?： boolean
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1Userinfo(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/userinfo', { headers: getHeaders(), ...options, params });
}

/**
 * @description 微信openid单点登录认证
 * @param params 参数 {
 *   token： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1WechatOpenidSso(params: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/wechat/openid/sso', { headers: getHeaders(), ...options, params });
}

/**
 * @description 浙江省域空间治理单点登录认证
 * @param params 参数 {
 *   ticketId?： string
 * }
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1ZjdgpSso(params: Record<string, any>, body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/zjdgp/sso', { body, headers: getHeaders(), ...options, params });
}

/**
 * @description 单点登录认证
 * @param params 参数 {
 *   code： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1CaAuth(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/ca/auth', { headers: getHeaders(), ...options, params });
}

/**
 * @description 退出登录
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1CaLogout(options: OptionsType = {}) {
  return get(apiName, '/v1/ca/logout', { headers: getHeaders(), ...options });
}

/**
 * @description sso验证
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1CaSso(options: OptionsType = {}) {
  return get(apiName, '/v1/ca/sso', { headers: getHeaders(), ...options });
}

/**
 * @description 获取CA的accessToken
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1CaToken(options: OptionsType = {}) {
  return get(apiName, '/v1/ca/token', { headers: getHeaders(), ...options });
}

/**
 * @description 扫码登录认证
 * @param params 参数 {
 *   code： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1DingtalkAuth(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/dingtalk/auth', { headers: getHeaders(), ...options, params });
}

/**
 * @description 同步浙政钉组织机构信息
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1DingtalkImportOrg(options: OptionsType = {}) {
  return get(apiName, '/v1/dingtalk/import/org', { headers: getHeaders(), ...options });
}

/**
 * @description 同步已选浙政钉组织机构信息
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1DingtalkImportOrg(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/dingtalk/import/org', { body, headers: getHeaders(), ...options });
}

/**
 * @description 同步浙政钉用户信息
 * @param params 参数 {
 *   orgId?： string
 *   isCascade?： boolean
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1DingtalkImportUser(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/dingtalk/import/user', { headers: getHeaders(), ...options, params });
}

/**
 * @description 同步浙政钉用户通讯录信息
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1DingtalkImportUserMobile(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/dingtalk/import/user/mobile', { body, headers: getHeaders(), ...options });
}

/**
 * @description 在浙政钉根机构搜索组织机构
 * @param params 参数 {
 *   keyword： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1DingtalkOrgSearch(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/dingtalk/org/search', { headers: getHeaders(), ...options, params });
}

/**
 * @description 应用切换测试
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1DingtalkTest(options: OptionsType = {}) {
  return get(apiName, '/v1/dingtalk/test', { headers: getHeaders(), ...options });
}

/**
 * @description 根据浙政钉机构accountId获取用户信息
 * @param params 参数 {
 *   accountId： integer
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1DingtalkUserAccount(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/dingtalk/user/account', { headers: getHeaders(), ...options, params });
}

/**
 * @description 批量获取用户详情
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1DingtalkUserDetailList(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/dingtalk/user/detail/list', { body, headers: getHeaders(), ...options });
}

/**
 * @description 批量根据浙政钉机构accountId获取用户信息
 * @param params 参数 {
 *   isDetail： boolean
 * }
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1DingtalkUserListAccount(params: Record<string, any>, body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/dingtalk/user/list/account', { body, headers: getHeaders(), ...options, params });
}

/**
 * @description 根据手机号查询用户
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1DingtalkUserMobile(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/dingtalk/user/mobile', { body, headers: getHeaders(), ...options });
}

/**
 * @description 根据手机号查询用户并导出查询结果
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1DingtalkUserMobileExport(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/dingtalk/user/mobile/export', { body, headers: getHeaders(), ...options });
}

/**
 * @description 在浙政钉根机构搜索用户
 * @param params 参数 {
 *   keyword： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1DingtalkUserSearch(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/dingtalk/user/search', { headers: getHeaders(), ...options, params });
}

/**
 * @description 根据免登授权码获取浙政钉用户信息
 * @param authCode string
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1DingtalkAuthCodeUserinfo(authCode: string, options: OptionsType = {}) {
  return get(apiName, `/v1/dingtalk/${authCode}/userinfo`, { headers: getHeaders(), ...options });
}

/**
 * @description 根据浙政钉机构 id 获取所分配的行政区划信息
 * @param orgId string
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1DingtalkOrgIdRegion(orgId: string, options: OptionsType = {}) {
  return get(apiName, `/v1/dingtalk/${orgId}/region`, { headers: getHeaders(), ...options });
}

/**
 * @description 根据浙政钉用户 id 获取用户的主职信息
 * @param userId string
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1DingtalkUserIdPosition(userId: string, options: OptionsType = {}) {
  return get(apiName, `/v1/dingtalk/${userId}/position`, { headers: getHeaders(), ...options });
}

/**
 * @description 根据浙政钉用户 id 获取用户职务信息
 * @param userId string
 * @param params 参数 {
 *   positionType： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1DingtalkUserIdPositionList(userId: string, params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, `/v1/dingtalk/${userId}/position/list`, { headers: getHeaders(), ...options, params });
}

/**
 * @description asyncMono
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1TestsAsyncMono(options: OptionsType = {}) {
  return get(apiName, '/v1/tests/async/mono', { headers: getHeaders(), ...options });
}

/**
 * @description asyncMonoCallable
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1TestsAsyncMonoCallable(options: OptionsType = {}) {
  return get(apiName, '/v1/tests/async/mono/callable', { headers: getHeaders(), ...options });
}

/**
 * @description 测试事务路由
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1TestsDo(options: OptionsType = {}) {
  return get(apiName, '/v1/tests/do', { headers: getHeaders(), ...options });
}

/**
 * @description 测试路由
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1TestsHello(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/tests/hello', { body, headers: getHeaders(), ...options });
}

/**
 * @description sync
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1TestsSync(options: OptionsType = {}) {
  return get(apiName, '/v1/tests/sync', { headers: getHeaders(), ...options });
}

/**
 * @description webclient
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1TestsWebclient(options: OptionsType = {}) {
  return get(apiName, '/v1/tests/webclient', { headers: getHeaders(), ...options });
}

/**
 * @description 执行定时删除服务操作日志方法
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function deleteV1LogApi(options: OptionsType = {}) {
  return del(apiName, '/v1/log/api', { headers: getHeaders(), ...options });
}

/**
 * @description 根据时间分组统计api请求信息
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1LogApiStatisticsOverview(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/log/api/statistics/overview', { body, headers: getHeaders(), ...options });
}

/**
 * @description 根据api所属app分页获取api请求情况
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1LogApiStatisticsOverviewApp(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/log/api/statistics/overview/app', { body, headers: getHeaders(), ...options });
}

/**
 * @description 根据终端操作系统分页获取api请求情况
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1LogApiStatisticsOverviewBrowser(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/log/api/statistics/overview/browser', { body, headers: getHeaders(), ...options });
}

/**
 * @description 根据http状态码对api调用次数进行统计
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1LogApiStatisticsOverviewHttpCode(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/log/api/statistics/overview/http-code', { body, headers: getHeaders(), ...options });
}

/**
 * @description 根据api所属app分页获取api请求情况
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1LogApiStatisticsOverviewOs(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/log/api/statistics/overview/os', { body, headers: getHeaders(), ...options });
}

/**
 * @description 根据api path 分页获取api请求情况
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1LogApiStatisticsOverviewPath(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/log/api/statistics/overview/path', { body, headers: getHeaders(), ...options });
}

/**
 * @description 批量删除应用日志
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function deleteV1LogAppBatch(body: Record<string, any>, options: OptionsType = {}) {
  return del(apiName, '/v1/log/app/batch', { body, headers: getHeaders(), ...options });
}

/**
 * @description 获取应用日志
 * @param params 参数 {
 *   apiDesc?： string
 *   app?： string
 *   domain?： string
 *   endTime?： integer
 *   index?： integer
 *   ip?： string
 *   ipRegion?： string
 *   isp?： string
 *   methodDesc?： string
 *   orgId?： string
 *   size?： integer
 *   startTime?： integer
 *   success?： boolean
 *   userId?： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1LogAppList(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/log/app/list', { headers: getHeaders(), ...options, params });
}

/**
 * @description 导出应用日志
 * @param params 参数 {
 *   apiDesc?： string
 *   app?： string
 *   domain?： string
 *   endTime?： integer
 *   index?： integer
 *   ip?： string
 *   ipRegion?： string
 *   isp?： string
 *   methodDesc?： string
 *   orgId?： string
 *   size?： integer
 *   startTime?： integer
 *   success?： boolean
 *   userId?： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1LogAppListExport(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/log/app/list/export', { headers: getHeaders(), ...options, params });
}

/**
 * @description 获取应用
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1LogAppType(options: OptionsType = {}) {
  return get(apiName, '/v1/log/app/type', { headers: getHeaders(), ...options });
}

/**
 * @description 存储客户端日志
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1LogClient(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/log/client', { body, headers: getHeaders(), ...options });
}

/**
 * @description 获取客户端日志
 * @param params 参数 {
 *   actionName?： string
 *   endTime?： integer
 *   index?： integer
 *   orgId?： string
 *   size?： integer
 *   startTime?： integer
 *   userId?： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1LogClientList(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/log/client/list', { headers: getHeaders(), ...options, params });
}

/**
 * @description 导出客户端日志
 * @param params 参数 {
 *   actionName?： string
 *   endTime?： integer
 *   index?： integer
 *   orgId?： string
 *   size?： integer
 *   startTime?： integer
 *   userId?： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1LogClientListExport(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/log/client/list/export', { headers: getHeaders(), ...options, params });
}

/**
 * @description 搜索操作
 * @param params 参数 {
 *   keyword?： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1LogClientSearchAction(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/log/client/search/action', { headers: getHeaders(), ...options, params });
}

/**
 * @description 搜索用户
 * @param params 参数 {
 *   keyword?： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1LogClientSearchUser(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/log/client/search/user', { headers: getHeaders(), ...options, params });
}

/**
 * @description 生成登录日志
 * @param params 参数 {
 *   startTime?： integer
 *   endTime?： integer
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1LogGen(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/log/gen', { headers: getHeaders(), ...options, params });
}

/**
 * @description 分页获取登录日志
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1LogLogin(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/log/login', { body, headers: getHeaders(), ...options });
}

/**
 * @description 执行定时删除pg登录日志方法
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function deleteV1LogLogin(options: OptionsType = {}) {
  return del(apiName, '/v1/log/login', { headers: getHeaders(), ...options });
}

/**
 * @description 查询登录次数
 * @param params 参数 {
 *   today?： boolean
 *   region?： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1LogLoginCount(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/log/login/count', { headers: getHeaders(), ...options, params });
}

/**
 * @description 登录次数统计
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1LogLoginCountStatistics(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/log/login/count/statistics', { body, headers: getHeaders(), ...options });
}

/**
 * @description 导出登录日志
 * @param params 参数 {
 *   userId?： string
 *   orgId?： string
 *   startTime?： integer
 *   endTime?： integer
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1LogLoginExport(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/log/login/export', { headers: getHeaders(), ...options, params });
}

/**
 * @description 查询登录日志
 * @param params 参数 {
 *   userId?： string
 *   orgId?： string
 *   startTime?： integer
 *   endTime?： integer
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1LogLoginList(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/log/login/list', { headers: getHeaders(), ...options, params });
}

/**
 * @description 分页获取登录日志（数据同步用）
 * @param params 参数 {
 *   startTime?： integer
 *   endTime?： integer
 *   index： integer
 *   size： integer
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1LogLoginListPage(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/log/login/list/page', { headers: getHeaders(), ...options, params });
}

/**
 * @description 主动拉取地区登录日志
 * @param params 参数 {
 *   lastSyncDate： integer
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1LogLoginLogPull(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/log/login/log/pull', { headers: getHeaders(), ...options, params });
}

/**
 * @description 登录次数统计-以机构分组
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1LogLoginOrgCountStatistics(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/log/login/org/count/statistics', { body, headers: getHeaders(), ...options });
}

/**
 * @description 分页获取登录用户
 * @param params 参数 {
 *   keyword?： string
 *   index?： integer
 *   size?： integer
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1LogLoginSearchUser(params: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/log/login/search/user', { headers: getHeaders(), ...options, params });
}

/**
 * @description 查询简易登录日志
 * @param params 参数 {
 *   userId?： string
 *   orgId?： string
 *   startTime?： integer
 *   endTime?： integer
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1LogLoginSimpleList(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/log/login/simple/list', { headers: getHeaders(), ...options, params });
}

/**
 * @description 分页获取简易登录日志（数据同步用）
 * @param params 参数 {
 *   startTime?： integer
 *   endTime?： integer
 *   index： integer
 *   size： integer
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1LogLoginSimpleListPage(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/log/login/simple/list/page', { headers: getHeaders(), ...options, params });
}

/**
 * @description 登录用户数量统计
 * @param params 参数 {
 *   startTime?： integer
 *   endTime?： integer
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1LogLoginUserCount(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/log/login/user/count', { headers: getHeaders(), ...options, params });
}

/**
 * @description 存储pv日志
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1LogPv(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/log/pv', { body, headers: getHeaders(), ...options });
}

/**
 * @description 搜索api描述
 * @param params 参数 {
 *   keyword?： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1LogSearchApi(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/log/search/api', { headers: getHeaders(), ...options, params });
}

/**
 * @description 搜索http响应码
 * @param params 参数 {
 *   keyword?： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1LogSearchHttpCode(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/log/search/http-code', { headers: getHeaders(), ...options, params });
}

/**
 * @description 搜索IP
 * @param params 参数 {
 *   keyword?： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1LogSearchIp(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/log/search/ip', { headers: getHeaders(), ...options, params });
}

/**
 * @description 搜索访问区域
 * @param params 参数 {
 *   keyword?： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1LogSearchIpRegion(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/log/search/ipRegion', { headers: getHeaders(), ...options, params });
}

/**
 * @description 搜索网络运营商
 * @param params 参数 {
 *   keyword?： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1LogSearchIsp(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/log/search/isp', { headers: getHeaders(), ...options, params });
}

/**
 * @description 搜索方法描述
 * @param params 参数 {
 *   keyword?： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1LogSearchMethod(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/log/search/method', { headers: getHeaders(), ...options, params });
}

/**
 * @description 搜索机构
 * @param params 参数 {
 *   keyword?： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1LogSearchOrg(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/log/search/org', { headers: getHeaders(), ...options, params });
}

/**
 * @description 搜索用户
 * @param params 参数 {
 *   keyword?： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1LogSearchUser(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/log/search/user', { headers: getHeaders(), ...options, params });
}

/**
 * @description 用户登录次数查询
 * @param params 参数 {
 *   userId： string
 *   startTime?： integer
 *   endTime?： integer
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1LogUserLoginCount(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/log/user/login/count', { headers: getHeaders(), ...options, params });
}

/**
 * @description 用户登录次数批量查询
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1LogUserLoginCount(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/log/user/login/count', { body, headers: getHeaders(), ...options });
}

/**
 * @description 查询访问次数
 * @param params 参数 {
 *   startTime?： integer
 *   endTime?： integer
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1LogVisitCount(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/log/visit/count', { headers: getHeaders(), ...options, params });
}

/**
 * @description 获取验证码
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getSsoAuthV1Captcha(options: OptionsType = {}) {
  return get(apiName, '/sso/auth/v1/captcha', { headers: getHeaders(), ...options });
}

/**
 * @description login
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postSsoLogin(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/sso/login', { body, headers: getHeaders(), ...options });
}

/**
 * @description 发送消息
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1MessageSend(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/message/send', { body, headers: getHeaders(), ...options });
}

/**
 * @description 获取在线用户
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1OnlineCount(options: OptionsType = {}) {
  return get(apiName, '/v1/online/count', { headers: getHeaders(), ...options });
}

/**
 * @description 获取在线用户
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1OnlineList(options: OptionsType = {}) {
  return get(apiName, '/v1/online/list', { headers: getHeaders(), ...options });
}

/**
 * @description 统计登录用户数
 * @param params 参数 {
 *   endTime： integer
 *   startTime： integer
 *   truncUnit： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1OnlineOverviewCount(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/online/overview/count', { headers: getHeaders(), ...options, params });
}

/**
 * @description 保存接口调用日志
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postOpenLog(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/open/log', { body, headers: getHeaders(), ...options });
}

/**
 * @description getOrgIdByUserId
 * @param id string
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getOrgOpenIdUserId(id: string, options: OptionsType = {}) {
  return get(apiName, `/org/open/id/user/${id}`, { headers: getHeaders(), ...options });
}

/**
 * @description 通过用户标识获取机构
 * @param id string
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getOrgOpenListUserId(id: string, options: OptionsType = {}) {
  return get(apiName, `/org/open/list/user/${id}`, { headers: getHeaders(), ...options });
}

/**
 * @description 通过用户标识获取机构
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getOrgOpenUser(options: OptionsType = {}) {
  return get(apiName, '/org/open/user', { headers: getHeaders(), ...options });
}

/**
 * @description 通过用户标识获取机构
 * @param id string
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getOrgOpenUserId(id: string, options: OptionsType = {}) {
  return get(apiName, `/org/open/user/${id}`, { headers: getHeaders(), ...options });
}

/**
 * @description listOrgIdByUserId
 * @param id string
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getOrgV2OpenIdUserId(id: string, options: OptionsType = {}) {
  return get(apiName, `/org/v2/open/id/user/${id}`, { headers: getHeaders(), ...options });
}

/**
 * @description 通过用户标识获取机构
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getOrgV2OpenUser(options: OptionsType = {}) {
  return get(apiName, '/org/v2/open/user', { headers: getHeaders(), ...options });
}

/**
 * @description getById
 * @param id string
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getOrgId(id: string, options: OptionsType = {}) {
  return get(apiName, `/org/${id}`, { headers: getHeaders(), ...options });
}

/**
 * @description 修改角色
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function putRoleOpen(body: Record<string, any>, options: OptionsType = {}) {
  return put(apiName, '/role/open', { body, headers: getHeaders(), ...options });
}

/**
 * @description 新建角色
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postRoleOpen(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/role/open', { body, headers: getHeaders(), ...options });
}

/**
 * @description 获取游客角色
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getRoleOpenGuest(options: OptionsType = {}) {
  return get(apiName, '/role/open/guest', { headers: getHeaders(), ...options });
}

/**
 * @description 获取系统所有角色id
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getRoleOpenIdList(options: OptionsType = {}) {
  return get(apiName, '/role/open/id/list', { headers: getHeaders(), ...options });
}

/**
 * @description 批量根据角色id和类型获取角色详细信息
 * @param params 参数 {
 *   roleTypes?： string
 * }
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postRoleOpenList(params: Record<string, any>, body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/role/open/list', { body, headers: getHeaders(), ...options, params });
}

/**
 * @description 根据角色名称获取角色列表
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postRoleOpenName(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/role/open/name', { body, headers: getHeaders(), ...options });
}

/**
 * @description 分页获取角色
 * @param params 参数 {
 *   index?： integer
 *   size?： integer
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getRoleOpenPage(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/role/open/page', { headers: getHeaders(), ...options, params });
}

/**
 * @description 搜索角色
 * @param params 参数 {
 *   keyword： string
 *   index?： integer
 *   size?： integer
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getRoleOpenSearch(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/role/open/search', { headers: getHeaders(), ...options, params });
}

/**
 * @description 分页获取角色
 * @param params 参数 {
 *   index?： integer
 *   size?： integer
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getRoleOpenSysPage(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/role/open/sys/page', { headers: getHeaders(), ...options, params });
}

/**
 * @description 获取当前用户对应管理类角色所授权的系统资源范围
 * @param resourceType string
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getRoleOpenSysResourceResourceType(resourceType: string, options: OptionsType = {}) {
  return get(apiName, `/role/open/sys/resource/${resourceType}`, { headers: getHeaders(), ...options });
}

/**
 * @description 获取角色授权的系统资源范围
 * @param roleId string
 * @param resourceType string
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getRoleOpenSysResourceRoleIdResourceType(roleId: string, resourceType: string, options: OptionsType = {}) {
  return get(apiName, `/role/open/sys/resource/${roleId}/${resourceType}`, { headers: getHeaders(), ...options });
}

/**
 * @description 根据类型获取角色
 * @param params 参数 {
 *   roleType： string
 *   keyword?： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postRoleOpenType(params: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/role/open/type', { headers: getHeaders(), ...options, params });
}

/**
 * @description 根据角色唯一名称获取角色列表
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postRoleOpenUname(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/role/open/uname', { body, headers: getHeaders(), ...options });
}

/**
 * @description 获取用户角色
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getRoleOpenUser(options: OptionsType = {}) {
  return get(apiName, '/role/open/user', { headers: getHeaders(), ...options });
}

/**
 * @description 获取用户角色
 * @param userId string
 * @param params 参数 {
 *   userSystemPlatform?： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getRoleOpenUserUserId(userId: string, params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, `/role/open/user/${userId}`, { headers: getHeaders(), ...options, params });
}

/**
 * @description 管理员搜索用户
 * @param params 参数 {
 *   orgId?： string
 *   userSystemPlatform： string
 *   isCascade： boolean
 *   keyword?： string
 *   index?： integer
 *   size?： integer
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getUserOpenAdminSearch(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/user/open/admin/search', { headers: getHeaders(), ...options, params });
}

/**
 * @description 为用户分配角色
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postUserOpenAssignUserRole(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/user/open/assign/user/role', { body, headers: getHeaders(), ...options });
}

/**
 * @description 取消用户分配角色
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function deleteUserOpenAssignUserRole(body: Record<string, any>, options: OptionsType = {}) {
  return del(apiName, '/user/open/assign/user/role', { body, headers: getHeaders(), ...options });
}

/**
 * @description 根据唯一id取消用户分配角色
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function deleteUserOpenAssignUserRoleById(body: Record<string, any>, options: OptionsType = {}) {
  return del(apiName, '/user/open/assign/user/role/byId', { body, headers: getHeaders(), ...options });
}

/**
 * @description 批量修改用户拓展信息
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function putUserOpenExtendedInfo(body: Record<string, any>, options: OptionsType = {}) {
  return put(apiName, '/user/open/extendedInfo', { body, headers: getHeaders(), ...options });
}

/**
 * @description 根据用户标识获取用户信息
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postUserOpenListUser(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/user/open/list/user', { body, headers: getHeaders(), ...options });
}

/**
 * @description 根据登录名获取用户信息
 * @param loginName string
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getUserOpenLoginNameLoginName(loginName: string, options: OptionsType = {}) {
  return get(apiName, `/user/open/loginName/${loginName}`, { headers: getHeaders(), ...options });
}

/**
 * @description 批量根据手机号获取用户标识
 * @param params 参数 {
 *   batchSize?： integer
 * }
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postUserOpenMobile(params: Record<string, any>, body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/user/open/mobile', { body, headers: getHeaders(), ...options, params });
}

/**
 * @description 获取角色已经分配的用户id
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postUserOpenRoleAssigned(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/user/open/role/assigned', { body, headers: getHeaders(), ...options });
}

/**
 * @description 获取角色已经分配的用户信息
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postUserOpenRoleAssignedDetail(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/user/open/role/assigned/detail', { body, headers: getHeaders(), ...options });
}

/**
 * @description 获取角色已经分配的用户基本信息
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postUserOpenRoleAssignedUser(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/user/open/role/assigned/user', { body, headers: getHeaders(), ...options });
}

/**
 * @description 获取角色下的用户
 * @param roleId string
 * @param index string
 * @param size string
 * @param params 参数 {
 *   userplatform?： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getUserOpenRoleRoleIdIndexSize(roleId: string, index: string, size: string, params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, `/user/open/role/${roleId}/${index}/${size}`, { headers: getHeaders(), ...options, params });
}

/**
 * @description 搜索用户
 * @param params 参数 {
 *   orgId?： string
 *   userSystemPlatform： string
 *   isCascade： boolean
 *   keyword?： string
 *   index?： integer
 *   size?： integer
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getUserOpenSearch(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/user/open/search', { headers: getHeaders(), ...options, params });
}

/**
 * @description 获取当前用户信息
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getUserOpenUser(options: OptionsType = {}) {
  return get(apiName, '/user/open/user', { headers: getHeaders(), ...options });
}

/**
 * @description 增加用户
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postUserOpenUser(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/user/open/user', { body, headers: getHeaders(), ...options });
}

/**
 * @description 批量获取用户详情
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postUserOpenUserDetailList(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/user/open/user/detail/list', { body, headers: getHeaders(), ...options });
}

/**
 * @description 获取用户附加信息
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getUserOpenUserInfo(options: OptionsType = {}) {
  return get(apiName, '/user/open/user/info', { headers: getHeaders(), ...options });
}

/**
 * @description 获取用户附加信息
 * @param id string
 * @param params 参数 {
 *   userSystemPlatform?： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getUserOpenUserInfoId(id: string, params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, `/user/open/user/info/${id}`, { headers: getHeaders(), ...options, params });
}

/**
 * @description 批量根据用户id获取用户角色
 * @param params 参数 {
 *   roleType： string
 * }
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postUserOpenUserRole(params: Record<string, any>, body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/user/open/user/role', { body, headers: getHeaders(), ...options, params });
}

/**
 * @description 根据用户标识获取用户信息
 * @param id string
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getUserOpenUserId(id: string, options: OptionsType = {}) {
  return get(apiName, `/user/open/user/${id}`, { headers: getHeaders(), ...options });
}

/**
 * @description 根据用户标识获取用户邮箱
 * @param id string
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getUserOpenUserIdEmail(id: string, options: OptionsType = {}) {
  return get(apiName, `/user/open/user/${id}/email`, { headers: getHeaders(), ...options });
}

/**
 * @description 根据用户标识获取用户手机号
 * @param id string
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getUserOpenUserIdMobile(id: string, options: OptionsType = {}) {
  return get(apiName, `/user/open/user/${id}/mobile`, { headers: getHeaders(), ...options });
}

/**
 * @description 为用户分配角色
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postUserV2OpenAssignUserRole(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/user/v2/open/assign/user/role', { body, headers: getHeaders(), ...options });
}

/**
 * @description 更新
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function putV1Org(body: Record<string, any>, options: OptionsType = {}) {
  return put(apiName, '/v1/org', { body, headers: getHeaders(), ...options });
}

/**
 * @description 添加
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1Org(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/org', { body, headers: getHeaders(), ...options });
}

/**
 * @description 根据用户id获取所在机构
 * @param id string
 * @param params 参数 {
 *   isSafe： boolean
 *   userSystemPlatform?： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1OrgByUserId(id: string, params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, `/v1/org/byUser/${id}`, { headers: getHeaders(), ...options, params });
}

/**
 * @description 批量获取机构
 * @param params 参数 {
 *   userSystemPlatform?： string
 * }
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1OrgList(params: Record<string, any>, body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/org/list', { body, headers: getHeaders(), ...options, params });
}

/**
 * @description 获取归属机构
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1OrgListBelong(options: OptionsType = {}) {
  return get(apiName, '/v1/org/list/belong', { headers: getHeaders(), ...options });
}

/**
 * @description 获取两节点之间的机构
 * @param params 参数 {
 *   topId?： string
 *   lowId?： string
 *   userSystemPlatform?： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1OrgListBetween(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/org/list/between', { headers: getHeaders(), ...options, params });
}

/**
 * @description 根据用户id获取所在机构列表
 * @param id string
 * @param params 参数 {
 *   userSystemPlatform?： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1OrgListByUserId(id: string, params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, `/v1/org/list/byUser/${id}`, { headers: getHeaders(), ...options, params });
}

/**
 * @description 获取下级归属单位
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1OrgListSub(options: OptionsType = {}) {
  return get(apiName, '/v1/org/list/sub', { headers: getHeaders(), ...options });
}

/**
 * @description 移动机构
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function putV1OrgMove(body: Record<string, any>, options: OptionsType = {}) {
  return put(apiName, '/v1/org/move', { body, headers: getHeaders(), ...options });
}

/**
 * @description 获取根组织机构
 * @param params 参数 {
 *   isFull?： boolean
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1OrgRoot(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/org/root', { headers: getHeaders(), ...options, params });
}

/**
 * @description 搜索机构
 * @param params 参数 {
 *   index?： integer
 *   isCascade?： boolean
 *   keyword： string
 *   orgId?： string
 *   size?： integer
 *   userSystemPlatform?： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1OrgSearch(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/org/search', { headers: getHeaders(), ...options, params });
}

/**
 * @description 获取机构树
 * @param params 参数 {
 *   id： string
 *   includeDept?： boolean
 *   userSystemPlatform?： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1OrgTree(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/org/tree', { headers: getHeaders(), ...options, params });
}

/**
 * @description 批量获取机构下用户总数
 * @param params 参数 {
 *   userSystemPlatform?： string
 * }
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1OrgUserCount(params: Record<string, any>, body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/org/user/count', { body, headers: getHeaders(), ...options, params });
}

/**
 * @description 重置机构用户关联数
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1OrgUserCountReset(options: OptionsType = {}) {
  return get(apiName, '/v1/org/user/count/reset', { headers: getHeaders(), ...options });
}

/**
 * @description 获取
 * @param id string
 * @param params 参数 {
 *   isSafe?： boolean
 *   userSystemPlatform?： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1OrgId(id: string, params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, `/v1/org/${id}`, { headers: getHeaders(), ...options, params });
}

/**
 * @description 删除
 * @param id string
 * @param params 参数 {
 *   userSystemPlatform?： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function deleteV1OrgId(id: string, params: Record<string, any>, options: OptionsType = {}) {
  return del(apiName, `/v1/org/${id}`, { headers: getHeaders(), ...options, params });
}

/**
 * @description 获取下级机构
 * @param id string
 * @param params 参数 {
 *   userSystemPlatform?： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1OrgIdChildren(id: string, params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, `/v1/org/${id}/children`, { headers: getHeaders(), ...options, params });
}

/**
 * @description 获取下级机构总数
 * @param id string
 * @param params 参数 {
 *   userSystemPlatform?： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1OrgIdChildrenCount(id: string, params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, `/v1/org/${id}/children/count`, { headers: getHeaders(), ...options, params });
}

/**
 * @description 获取组织机构的行政区划
 * @param orgId string
 * @param params 参数 {
 *   userSystemPlatform?： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1OrgOrgIdRegion(orgId: string, params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, `/v1/org/${orgId}/region`, { headers: getHeaders(), ...options, params });
}

/**
 * @description 批量导出日志
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postExport(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/export', { body, headers: getHeaders(), ...options });
}

/**
 * @description 获取
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getList(options: OptionsType = {}) {
  return get(apiName, '/list', { headers: getHeaders(), ...options });
}

/**
 * @description 更新角色
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function putV1Role(body: Record<string, any>, options: OptionsType = {}) {
  return put(apiName, '/v1/role', { body, headers: getHeaders(), ...options });
}

/**
 * @description 添加角色
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1Role(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/role', { body, headers: getHeaders(), ...options });
}

/**
 * @description 给用户组分配角色
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function putV1RoleAssignOrg(body: Record<string, any>, options: OptionsType = {}) {
  return put(apiName, '/v1/role/assign/org', { body, headers: getHeaders(), ...options });
}

/**
 * @description 为角色分配管理端资源
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1RoleAssignSys(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/role/assign/sys', { body, headers: getHeaders(), ...options });
}

/**
 * @description 获取当前操作可分配角色
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1RoleAssignable(options: OptionsType = {}) {
  return get(apiName, '/v1/role/assignable', { headers: getHeaders(), ...options });
}

/**
 * @description 获取组织机构下可分配角色
 * @param orgId string
 * @param params 参数 {
 *   userSystemPlatform?： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1RoleAssignableOrgId(orgId: string, params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, `/v1/role/assignable/${orgId}`, { headers: getHeaders(), ...options, params });
}

/**
 * @description 获取角色已关联的所有机构以及机构下用户(完整上下级机构列表)
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1RoleAssignedFull(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/role/assigned/full', { body, headers: getHeaders(), ...options });
}

/**
 * @description 获取角色已关联的所有机构以及机构下用户(完整上下级机构列表)
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1RoleAssignedOrgFull(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/role/assigned/org/full', { body, headers: getHeaders(), ...options });
}

/**
 * @description 获取机构已经分配的角色
 * @param orgId string
 * @param params 参数 {
 *   userSystemPlatform?： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1RoleAssignedOrgOrgId(orgId: string, params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, `/v1/role/assigned/org/${orgId}`, { headers: getHeaders(), ...options, params });
}

/**
 * @description 获取角色已关联的用户(完整上下级机构和用户列表)
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1RoleAssignedUserFull(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/role/assigned/user/full', { body, headers: getHeaders(), ...options });
}

/**
 * @description 获取用户已经分配的角色
 * @param userId string
 * @param params 参数 {
 *   userSystemPlatform?： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1RoleAssignedUserUserId(userId: string, params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, `/v1/role/assigned/user/${userId}`, { headers: getHeaders(), ...options, params });
}

/**
 * @description 批量删除角色
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function deleteV1RoleBatch(body: Record<string, any>, options: OptionsType = {}) {
  return del(apiName, '/v1/role/batch', { body, headers: getHeaders(), ...options });
}

/**
 * @description 角色关联组织机构
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1RoleBatchAssignOrg(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/role/batch/assign/org', { body, headers: getHeaders(), ...options });
}

/**
 * @description 角色关联用户
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1RoleBatchAssignUser(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/role/batch/assign/user', { body, headers: getHeaders(), ...options });
}

/**
 * @description 导出角色已授权的用户
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1RoleExportAssigned(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/role/export/assigned', { body, headers: getHeaders(), ...options });
}

/**
 * @description 修改角色分组名称
 * @param params 参数 {
 *   oldName： string
 *   newName： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function putV1RoleGroup(params: Record<string, any>, options: OptionsType = {}) {
  return put(apiName, '/v1/role/group', { headers: getHeaders(), ...options, params });
}

/**
 * @description 获取所有角色分组
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1RoleGroup(options: OptionsType = {}) {
  return post(apiName, '/v1/role/group', { headers: getHeaders(), ...options });
}

/**
 * @description 获取角色列表
 * @param params 参数 {
 *   keyword?： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1RoleList(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/role/list', { headers: getHeaders(), ...options, params });
}

/**
 * @description 获取机构下角色
 * @param orgId string
 * @param params 参数 {
 *   userSystemPlatform?： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1RoleListOrgOrgId(orgId: string, params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, `/v1/role/list/org/${orgId}`, { headers: getHeaders(), ...options, params });
}

/**
 * @description 根据角色业务类型获取角色列表
 * @param roleType string
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1RoleListRoleType(roleType: string, options: OptionsType = {}) {
  return get(apiName, `/v1/role/list/${roleType}`, { headers: getHeaders(), ...options });
}

/**
 * @description 分页获取角色
 * @param params 参数 {
 *   keyword?： string
 *   index?： integer
 *   size?： integer
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1RoleQuery(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/role/query', { headers: getHeaders(), ...options, params });
}

/**
 * @description 获取角色应用范围
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1RoleScope(options: OptionsType = {}) {
  return get(apiName, '/v1/role/scope', { headers: getHeaders(), ...options });
}

/**
 * @description 删除角色的管理端资源
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function deleteV1RoleSysResource(body: Record<string, any>, options: OptionsType = {}) {
  return del(apiName, '/v1/role/sys/resource', { body, headers: getHeaders(), ...options });
}

/**
 * @description 获取角色的管理端资源
 * @param roleId string
 * @param resourceType string
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1RoleSysResourceRoleIdResourceType(roleId: string, resourceType: string, options: OptionsType = {}) {
  return get(apiName, `/v1/role/sys/resource/${roleId}/${resourceType}`, { headers: getHeaders(), ...options });
}

/**
 * @description 获取角色类型
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1RoleType(options: OptionsType = {}) {
  return get(apiName, '/v1/role/type', { headers: getHeaders(), ...options });
}

/**
 * @description 获取角色
 * @param id string
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1RoleId(id: string, options: OptionsType = {}) {
  return get(apiName, `/v1/role/${id}`, { headers: getHeaders(), ...options });
}

/**
 * @description 删除角色
 * @param id string
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function deleteV1RoleId(id: string, options: OptionsType = {}) {
  return del(apiName, `/v1/role/${id}`, { headers: getHeaders(), ...options });
}

/**
 * @description 获取角色已关联的机构
 * @param roleId string
 * @param params 参数 {
 *   all?： boolean
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1RoleRoleIdAssignedOrg(roleId: string, params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, `/v1/role/${roleId}/assigned/org`, { headers: getHeaders(), ...options, params });
}

/**
 * @description 获取角色已关联的用户
 * @param roleId string
 * @param params 参数 {
 *   all?： boolean
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1RoleRoleIdAssignedUser(roleId: string, params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, `/v1/role/${roleId}/assigned/user`, { headers: getHeaders(), ...options, params });
}

/**
 * @description 获取角色已关联的用户(完整上下级机构和用户列表)
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV2RoleAssignedUserFull(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v2/role/assigned/user/full', { body, headers: getHeaders(), ...options });
}

/**
 * @description 修改用户信息
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function putV1User(body: Record<string, any>, options: OptionsType = {}) {
  return put(apiName, '/v1/user', { body, headers: getHeaders(), ...options });
}

/**
 * @description 添加用户
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1User(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/user', { body, headers: getHeaders(), ...options });
}

/**
 * @description 给用户分配角色
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function putV1UserAssignRole(body: Record<string, any>, options: OptionsType = {}) {
  return put(apiName, '/v1/user/assign/role', { body, headers: getHeaders(), ...options });
}

/**
 * @description 批量删除用户
 * @param params 参数 {
 *   userSystemPlatform?： string
 * }
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function deleteV1UserBatch(params: Record<string, any>, body: Record<string, any>, options: OptionsType = {}) {
  return del(apiName, '/v1/user/batch', { body, headers: getHeaders(), ...options, params });
}

/**
 * @description 用户修改密码
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function putV1UserClientPassword(body: Record<string, any>, options: OptionsType = {}) {
  return put(apiName, '/v1/user/client/password', { body, headers: getHeaders(), ...options });
}

/**
 * @description 导出用户
 * @param params 参数 {
 *   orgId?： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1UserExport(params: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/user/export', { headers: getHeaders(), ...options, params });
}

/**
 * @description 导入用户
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1UserImport(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/user/import', { body, headers: getHeaders(), ...options });
}

/**
 * @description 获取导入用户模板
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1UserImportTemplate(options: OptionsType = {}) {
  return get(apiName, '/v1/user/import/template', { headers: getHeaders(), ...options });
}

/**
 * @description 批量获取用户详情
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1UserList(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/user/list', { body, headers: getHeaders(), ...options });
}

/**
 * @description 批量获取用户详情
 * @param params 参数 {
 *   isDetail?： boolean
 * }
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1UserListByAccountId(params: Record<string, any>, body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/user/list/byAccountId', { body, headers: getHeaders(), ...options, params });
}

/**
 * @description 获取机构下用户列表
 * @param orgId string
 * @param params 参数 {
 *   cascade?： boolean
 *   userSystemPlatform?： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1UserListOrgOrgId(orgId: string, params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, `/v1/user/list/org/${orgId}`, { headers: getHeaders(), ...options, params });
}

/**
 * @description 修改用户机构信息
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function putV1UserOrg(body: Record<string, any>, options: OptionsType = {}) {
  return put(apiName, '/v1/user/org', { body, headers: getHeaders(), ...options });
}

/**
 * @description 删除用户机构信息
 * @param params 参数 {
 *   userId： string
 *   orgId： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function deleteV1UserOrg(params: Record<string, any>, options: OptionsType = {}) {
  return del(apiName, '/v1/user/org', { headers: getHeaders(), ...options, params });
}

/**
 * @description 分页获取机构下用户列表
 * @param orgId string
 * @param params 参数 {
 *   cascade?： boolean
 *   keyword?： string
 *   userSystemPlatform?： string
 *   index?： integer
 *   size?： integer
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1UserOrgOrgId(orgId: string, params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, `/v1/user/org/${orgId}`, { headers: getHeaders(), ...options, params });
}

/**
 * @description 修改用户密码
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function putV1UserPassword(body: Record<string, any>, options: OptionsType = {}) {
  return put(apiName, '/v1/user/password', { body, headers: getHeaders(), ...options });
}

/**
 * @description 查看角色关联的用户
 * @param roleId string
 * @param params 参数 {
 *   keyword?： string
 *   userplatform?： string
 *   index?： integer
 *   size?： integer
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1UserRoleRoleId(roleId: string, params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, `/v1/user/role/${roleId}`, { headers: getHeaders(), ...options, params });
}

/**
 * @description 搜索机构下用户
 * @param params 参数 {
 *   keyword： string
 *   orgId?： string
 *   isCascade： boolean
 *   userSystemPlatform?： string
 *   index?： integer
 *   size?： integer
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1UserSearch(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/user/search', { headers: getHeaders(), ...options, params });
}

/**
 * @description 搜索内置用户
 * @param params 参数 {
 *   index?： integer
 *   keyword?： string
 *   size?： integer
 *   all?： boolean
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1UserSysSearch(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/user/sys/search', { headers: getHeaders(), ...options, params });
}

/**
 * @description 管理员修改密码
 * @param userId string
 * @param params 参数 {
 *   userSystemPlatform?： string
 * }
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function putV1UserSysUserIdPassword(userId: string, params: Record<string, any>, body: Record<string, any>, options: OptionsType = {}) {
  return put(apiName, `/v1/user/sys/${userId}/password`, { body, headers: getHeaders(), ...options, params });
}

/**
 * @description 修改密码加密策略
 * @param params 参数 {
 *   targetEncryptType： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1UserUpdatePass(params: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/user/update/pass', { headers: getHeaders(), ...options, params });
}

/**
 * @description 获取用户详情
 * @param id string
 * @param params 参数 {
 *   isSafe?： boolean
 *   userSystemPlatform?： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1UserId(id: string, params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, `/v1/user/${id}`, { headers: getHeaders(), ...options, params });
}

/**
 * @description 删除用户
 * @param id string
 * @param params 参数 {
 *   userSystemPlatform?： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function deleteV1UserId(id: string, params: Record<string, any>, options: OptionsType = {}) {
  return del(apiName, `/v1/user/${id}`, { headers: getHeaders(), ...options, params });
}

/**
 * @description 获取用户手机号
 * @param id string
 * @param params 参数 {
 *   userSystemPlatform?： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1UserIdMobile(id: string, params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, `/v1/user/${id}/mobile`, { headers: getHeaders(), ...options, params });
}

/**
 * @description 修改用户状态
 * @param id string
 * @param params 参数 {
 *   state： integer
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1UserIdState(id: string, params: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, `/v1/user/${id}/state`, { headers: getHeaders(), ...options, params });
}

/**
 * @description 企业微信扫码登录认证
 * @param params 参数 {
 *   code： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1WeworkAuth(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/wework/auth', { headers: getHeaders(), ...options, params });
}

/**
 * @description 同步组织机构信息
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1WeworkImportOrg(options: OptionsType = {}) {
  return get(apiName, '/v1/wework/import/org', { headers: getHeaders(), ...options });
}

/**
 * @description 同步用户信息
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1WeworkImportUser(options: OptionsType = {}) {
  return get(apiName, '/v1/wework/import/user', { headers: getHeaders(), ...options });
}

/**
 * @description 更新法人用户信息
 * @param params 参数 {
 *   userId?： string
 * }
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1ZlbLegalInfo(params: Record<string, any>, body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/zlb/legal/info', { body, headers: getHeaders(), ...options, params });
}

/**
 * @description 浙里办法人认证回调
 * @param params 参数 {
 *   goto?： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1ZlbLegalPersonSsoCallback(params: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/zlb/legal/person/sso/callback', { headers: getHeaders(), ...options, params });
}

/**
 * @description 浙里办移动端ticket进行认证
 * @param ticket string
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1ZlbMobileSsoTicket(ticket: string, options: OptionsType = {}) {
  return get(apiName, `/v1/zlb/mobile/sso/${ticket}`, { headers: getHeaders(), ...options });
}

/**
 * @description 更新个人用户信息
 * @param params 参数 {
 *   userId?： string
 * }
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1ZlbPersonalInfo(params: Record<string, any>, body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/zlb/personal/info', { body, headers: getHeaders(), ...options, params });
}

/**
 * @description 个人用户通过浙里办ticket进行认证
 * @param ticket string
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1ZlbPersonalSsoTicket(ticket: string, options: OptionsType = {}) {
  return get(apiName, `/v1/zlb/personal/sso/${ticket}`, { headers: getHeaders(), ...options });
}

/**
 * @description 模糊检索行政区划（已过时，切换到接口：/v2/zonings/searchbyscope，或/v2/zonings/searchbyversion）
 * @param params 参数 {
 *   pageindex： integer
 *   pagesize： integer
 *   pzc?： string
 *   keyword： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1Zonings(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/zonings', { headers: getHeaders(), ...options, params });
}

/**
 * @description 更新行政区划
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function putV1Zonings(body: Record<string, any>, options: OptionsType = {}) {
  return put(apiName, '/v1/zonings', { body, headers: getHeaders(), ...options });
}

/**
 * @description 添加行政区划
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1Zonings(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/zonings', { body, headers: getHeaders(), ...options });
}

/**
 * @description 删除行政区划（传入区划id数组）
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function deleteV1Zonings(body: Record<string, any>, options: OptionsType = {}) {
  return del(apiName, '/v1/zonings', { body, headers: getHeaders(), ...options });
}

/**
 * @description 设置默认的行政区版本
 * @param versionId string
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function putV1ZoningsDefaultVersionId(versionId: string, options: OptionsType = {}) {
  return put(apiName, `/v1/zonings/default/${versionId}`, { headers: getHeaders(), ...options });
}

/**
 * @description 根据 id或者 code获取行政区划信息
 * @param params 参数 {
 *   zoningId?： string
 *   versionId?： string
 *   zoningCode?： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1ZoningsInfo(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/zonings/info', { headers: getHeaders(), ...options, params });
}

/**
 * @description 区划级别
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1ZoningsLevel(options: OptionsType = {}) {
  return get(apiName, '/v1/zonings/level', { headers: getHeaders(), ...options });
}

/**
 * @description 获取行政区划列表
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1ZoningsList(options: OptionsType = {}) {
  return get(apiName, '/v1/zonings/list', { headers: getHeaders(), ...options });
}

/**
 * @description 根据行政区划id获取获取行政区划列表
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1ZoningsListInfo(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/zonings/list/info', { body, headers: getHeaders(), ...options });
}

/**
 * @description 获取行政区划父级行政区代码
 * @param params 参数 {
 *   versionId?： string
 * }
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1ZoningsListParentCode(params: Record<string, any>, body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/zonings/list/parent/code', { body, headers: getHeaders(), ...options, params });
}

/**
 * @description 更新行政区服务
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function putV1ZoningsMap(body: Record<string, any>, options: OptionsType = {}) {
  return put(apiName, '/v1/zonings/map', { body, headers: getHeaders(), ...options });
}

/**
 * @description 新增行政区服务配置
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1ZoningsMap(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/zonings/map', { body, headers: getHeaders(), ...options });
}

/**
 * @description 删除行政区服务
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function deleteV1ZoningsMap(body: Record<string, any>, options: OptionsType = {}) {
  return del(apiName, '/v1/zonings/map', { body, headers: getHeaders(), ...options });
}

/**
 * @description 获取行政区服务详细信息
 * @param id string
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1ZoningsMapId(id: string, options: OptionsType = {}) {
  return get(apiName, `/v1/zonings/map/${id}`, { headers: getHeaders(), ...options });
}

/**
 * @description 获取当前登录者的行政区划树形数据
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1ZoningsMyTree(options: OptionsType = {}) {
  return get(apiName, '/v1/zonings/my/tree', { headers: getHeaders(), ...options });
}

/**
 * @description 获取行政区划path
 * @param params 参数 {
 *   code： string
 *   versionId?： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1ZoningsPath(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/zonings/path', { headers: getHeaders(), ...options, params });
}

/**
 * @description 根据行政区编码，获取行政区划子级树形数据
 * @param parentZoningCode string
 * @param params 参数 {
 *   allchildren?： boolean
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1ZoningsSubtreeParentZoningCode(parentZoningCode: string, params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, `/v1/zonings/subtree/${parentZoningCode}`, { headers: getHeaders(), ...options, params });
}

/**
 * @description 获取行政区划树形数据
 * @param params 参数 {
 *   parentid?： string
 *   allchildren?： boolean
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1ZoningsTree(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/zonings/tree', { headers: getHeaders(), ...options, params });
}

/**
 * @description 行政区上下移动（同级）
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1ZoningsUpdown(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/zonings/updown', { body, headers: getHeaders(), ...options });
}

/**
 * @description 根据scope标识获取行政区版本
 * @param params 参数 {
 *   scope： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1ZoningsVersion(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/zonings/version', { headers: getHeaders(), ...options, params });
}

/**
 * @description 更新版本
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function putV1ZoningsVersion(body: Record<string, any>, options: OptionsType = {}) {
  return put(apiName, '/v1/zonings/version', { body, headers: getHeaders(), ...options });
}

/**
 * @description 添加新版本
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1ZoningsVersion(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/zonings/version', { body, headers: getHeaders(), ...options });
}

/**
 * @description 拷贝行政区划版本以及行政区数据
 * @param versionId string
 * @param params 参数 {
 *   versionId： string
 *   name?： string
 *   scope： string
 *   containdata： boolean
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1ZoningsVersionCopyVersionId(versionId: string, params: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, `/v1/zonings/version/copy/${versionId}`, { headers: getHeaders(), ...options, params });
}

/**
 * @description 获取行政区版本列表
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1ZoningsVersions(options: OptionsType = {}) {
  return get(apiName, '/v1/zonings/versions', { headers: getHeaders(), ...options });
}

/**
 * @description 获取行政区服务列表
 * @param versionId string
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1ZoningsVersionIdMapList(versionId: string, options: OptionsType = {}) {
  return get(apiName, `/v1/zonings/${versionId}/map/list`, { headers: getHeaders(), ...options });
}

/**
 * @description 导出行政区划
 * @param params 参数 {
 *   versionId?： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV2ZoningsExport(params: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v2/zonings/export', { headers: getHeaders(), ...options, params });
}

/**
 * @description 导出行政区划模板
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV2ZoningsExportTemplate(options: OptionsType = {}) {
  return get(apiName, '/v2/zonings/export/template', { headers: getHeaders(), ...options });
}

/**
 * @description 导入行政区划
 * @param params 参数 {
 *   versionId?： string
 * }
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV2ZoningsImport(params: Record<string, any>, body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v2/zonings/import', { body, headers: getHeaders(), ...options, params });
}

/**
 * @description 获取行政区划列表
 * @param versionId string
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV2ZoningsListVersionId(versionId: string, options: OptionsType = {}) {
  return get(apiName, `/v2/zonings/list/${versionId}`, { headers: getHeaders(), ...options });
}

/**
 * @description 根据适用范围过滤模糊检索行政区划
 * @param params 参数 {
 *   scope： string
 *   pageindex： integer
 *   pagesize： integer
 *   pzc?： string
 *   keyword： string
 *   levels?： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV2ZoningsSearchbyscope(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v2/zonings/searchbyscope', { headers: getHeaders(), ...options, params });
}

/**
 * @description 根据版本id过滤模糊检索行政区划
 * @param params 参数 {
 *   vid： string
 *   pageindex： integer
 *   pagesize： integer
 *   pzc?： string
 *   keyword： string
 *   levels?： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV2ZoningsSearchbyversion(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v2/zonings/searchbyversion', { headers: getHeaders(), ...options, params });
}

/**
 * @description 根据行政区编码，获取行政区划子级树形数据
 * @param params 参数 {
 *   versionId?： string
 *   parentZoningCode： string
 *   allChildren?： boolean
 *   standard?： boolean
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV2ZoningsSubtree(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v2/zonings/subtree', { headers: getHeaders(), ...options, params });
}

/**
 * @description 根据行政区编码和适用范围唯一值，获取行政区划子级树形数据
 * @param parentZoningCode string
 * @param params 参数 {
 *   scope： string
 *   allchildren?： boolean
 *   standard?： boolean
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV2ZoningsSubtreeParentZoningCode(parentZoningCode: string, params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, `/v2/zonings/subtree/${parentZoningCode}`, { headers: getHeaders(), ...options, params });
}

/**
 * @description 根据行政区编码，获取行政区划子级树形数据
 * @param versionId string
 * @param parentZoningCode string
 * @param params 参数 {
 *   allchildren?： boolean
 *   standard?： boolean
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV2ZoningsSubtreeVersionIdParentZoningCode(versionId: string, parentZoningCode: string, params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, `/v2/zonings/subtree/${versionId}/${parentZoningCode}`, { headers: getHeaders(), ...options, params });
}

/**
 * @description 获取行政区划树形数据
 * @param versionId string
 * @param params 参数 {
 *   parentid?： string
 *   allchildren?： boolean
 *   standard?： boolean
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV2ZoningsTreeVersionId(versionId: string, params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, `/v2/zonings/tree/${versionId}`, { headers: getHeaders(), ...options, params });
}

/**
 * @description 更新行政区划
 * @param versionId string
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function putV2ZoningsVersionId(versionId: string, body: Record<string, any>, options: OptionsType = {}) {
  return put(apiName, `/v2/zonings/${versionId}`, { body, headers: getHeaders(), ...options });
}

/**
 * @description 添加行政区划
 * @param versionId string
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV2ZoningsVersionId(versionId: string, body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, `/v2/zonings/${versionId}`, { body, headers: getHeaders(), ...options });
}

/**
 * @description 删除行政区划（传入区划id数组）
 * @param versionId string
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function deleteV2ZoningsVersionId(versionId: string, body: Record<string, any>, options: OptionsType = {}) {
  return del(apiName, `/v2/zonings/${versionId}`, { body, headers: getHeaders(), ...options });
}

/**
 * @description 删除行政区划版本
 * @param versionId string
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function deleteV2ZoningsVersionIdAll(versionId: string, options: OptionsType = {}) {
  return del(apiName, `/v2/zonings/${versionId}/all`, { headers: getHeaders(), ...options });
}

export const collieApi = {
  getV1Api,
  putV1Api,
  postV1Api,
  deleteV1Api,
  putV1ApiAppPermission,
  postV1ApiAppPermission,
  deleteV1ApiAppPermission,
  putV1ApiAppPermissionEnable,
  getV1ApiAppPermissionPageIndexSize,
  getV1ApiAppPermissionId,
  getV1ApiAuthorizationPermission,
  postV1ApiAuthorizationPermission,
  putV1ApiEnable,
  getV1ApiListApp,
  getV1ApiListTag,
  getV1ApiTimeunit,
  putV1App,
  postV1App,
  getV1AppList,
  getV1AppId,
  putV1AppId,
  deleteV1AppId,
  getV1AuthorizationToken,
  getV1Decrypt,
  getV1DingtalkSsoAuthCode,
  getV1Encrypt,
  getV1Logout,
  postV1NbsyptSso,
  getV1Sso,
  getV1SsoToken,
  getV1Ticket,
  getV1Userinfo,
  postV1WechatOpenidSso,
  postV1ZjdgpSso,
  getV1CaAuth,
  getV1CaLogout,
  getV1CaSso,
  getV1CaToken,
  getV1DingtalkAuth,
  getV1DingtalkImportOrg,
  postV1DingtalkImportOrg,
  getV1DingtalkImportUser,
  postV1DingtalkImportUserMobile,
  getV1DingtalkOrgSearch,
  getV1DingtalkTest,
  getV1DingtalkUserAccount,
  postV1DingtalkUserDetailList,
  postV1DingtalkUserListAccount,
  postV1DingtalkUserMobile,
  postV1DingtalkUserMobileExport,
  getV1DingtalkUserSearch,
  getV1DingtalkAuthCodeUserinfo,
  getV1DingtalkOrgIdRegion,
  getV1DingtalkUserIdPosition,
  getV1DingtalkUserIdPositionList,
  getV1TestsAsyncMono,
  getV1TestsAsyncMonoCallable,
  getV1TestsDo,
  postV1TestsHello,
  getV1TestsSync,
  getV1TestsWebclient,
  deleteV1LogApi,
  postV1LogApiStatisticsOverview,
  postV1LogApiStatisticsOverviewApp,
  postV1LogApiStatisticsOverviewBrowser,
  postV1LogApiStatisticsOverviewHttpCode,
  postV1LogApiStatisticsOverviewOs,
  postV1LogApiStatisticsOverviewPath,
  deleteV1LogAppBatch,
  getV1LogAppList,
  getV1LogAppListExport,
  getV1LogAppType,
  postV1LogClient,
  getV1LogClientList,
  getV1LogClientListExport,
  getV1LogClientSearchAction,
  getV1LogClientSearchUser,
  getV1LogGen,
  postV1LogLogin,
  deleteV1LogLogin,
  getV1LogLoginCount,
  postV1LogLoginCountStatistics,
  getV1LogLoginExport,
  getV1LogLoginList,
  getV1LogLoginListPage,
  getV1LogLoginLogPull,
  postV1LogLoginOrgCountStatistics,
  postV1LogLoginSearchUser,
  getV1LogLoginSimpleList,
  getV1LogLoginSimpleListPage,
  getV1LogLoginUserCount,
  postV1LogPv,
  getV1LogSearchApi,
  getV1LogSearchHttpCode,
  getV1LogSearchIp,
  getV1LogSearchIpRegion,
  getV1LogSearchIsp,
  getV1LogSearchMethod,
  getV1LogSearchOrg,
  getV1LogSearchUser,
  getV1LogUserLoginCount,
  postV1LogUserLoginCount,
  getV1LogVisitCount,
  getSsoAuthV1Captcha,
  postSsoLogin,
  postV1MessageSend,
  getV1OnlineCount,
  getV1OnlineList,
  getV1OnlineOverviewCount,
  postOpenLog,
  getOrgOpenIdUserId,
  getOrgOpenListUserId,
  getOrgOpenUser,
  getOrgOpenUserId,
  getOrgV2OpenIdUserId,
  getOrgV2OpenUser,
  getOrgId,
  putRoleOpen,
  postRoleOpen,
  getRoleOpenGuest,
  getRoleOpenIdList,
  postRoleOpenList,
  postRoleOpenName,
  getRoleOpenPage,
  getRoleOpenSearch,
  getRoleOpenSysPage,
  getRoleOpenSysResourceResourceType,
  getRoleOpenSysResourceRoleIdResourceType,
  postRoleOpenType,
  postRoleOpenUname,
  getRoleOpenUser,
  getRoleOpenUserUserId,
  getUserOpenAdminSearch,
  postUserOpenAssignUserRole,
  deleteUserOpenAssignUserRole,
  deleteUserOpenAssignUserRoleById,
  putUserOpenExtendedInfo,
  postUserOpenListUser,
  getUserOpenLoginNameLoginName,
  postUserOpenMobile,
  postUserOpenRoleAssigned,
  postUserOpenRoleAssignedDetail,
  postUserOpenRoleAssignedUser,
  getUserOpenRoleRoleIdIndexSize,
  getUserOpenSearch,
  getUserOpenUser,
  postUserOpenUser,
  postUserOpenUserDetailList,
  getUserOpenUserInfo,
  getUserOpenUserInfoId,
  postUserOpenUserRole,
  getUserOpenUserId,
  getUserOpenUserIdEmail,
  getUserOpenUserIdMobile,
  postUserV2OpenAssignUserRole,
  putV1Org,
  postV1Org,
  getV1OrgByUserId,
  postV1OrgList,
  getV1OrgListBelong,
  getV1OrgListBetween,
  getV1OrgListByUserId,
  getV1OrgListSub,
  putV1OrgMove,
  getV1OrgRoot,
  getV1OrgSearch,
  getV1OrgTree,
  postV1OrgUserCount,
  getV1OrgUserCountReset,
  getV1OrgId,
  deleteV1OrgId,
  getV1OrgIdChildren,
  getV1OrgIdChildrenCount,
  getV1OrgOrgIdRegion,
  postExport,
  getList,
  putV1Role,
  postV1Role,
  putV1RoleAssignOrg,
  postV1RoleAssignSys,
  getV1RoleAssignable,
  getV1RoleAssignableOrgId,
  postV1RoleAssignedFull,
  postV1RoleAssignedOrgFull,
  getV1RoleAssignedOrgOrgId,
  postV1RoleAssignedUserFull,
  getV1RoleAssignedUserUserId,
  deleteV1RoleBatch,
  postV1RoleBatchAssignOrg,
  postV1RoleBatchAssignUser,
  postV1RoleExportAssigned,
  putV1RoleGroup,
  postV1RoleGroup,
  getV1RoleList,
  getV1RoleListOrgOrgId,
  getV1RoleListRoleType,
  getV1RoleQuery,
  getV1RoleScope,
  deleteV1RoleSysResource,
  getV1RoleSysResourceRoleIdResourceType,
  getV1RoleType,
  getV1RoleId,
  deleteV1RoleId,
  getV1RoleRoleIdAssignedOrg,
  getV1RoleRoleIdAssignedUser,
  postV2RoleAssignedUserFull,
  putV1User,
  postV1User,
  putV1UserAssignRole,
  deleteV1UserBatch,
  putV1UserClientPassword,
  postV1UserExport,
  postV1UserImport,
  getV1UserImportTemplate,
  postV1UserList,
  postV1UserListByAccountId,
  getV1UserListOrgOrgId,
  putV1UserOrg,
  deleteV1UserOrg,
  getV1UserOrgOrgId,
  putV1UserPassword,
  getV1UserRoleRoleId,
  getV1UserSearch,
  getV1UserSysSearch,
  putV1UserSysUserIdPassword,
  postV1UserUpdatePass,
  getV1UserId,
  deleteV1UserId,
  getV1UserIdMobile,
  postV1UserIdState,
  getV1WeworkAuth,
  getV1WeworkImportOrg,
  getV1WeworkImportUser,
  postV1ZlbLegalInfo,
  postV1ZlbLegalPersonSsoCallback,
  getV1ZlbMobileSsoTicket,
  postV1ZlbPersonalInfo,
  getV1ZlbPersonalSsoTicket,
  getV1Zonings,
  putV1Zonings,
  postV1Zonings,
  deleteV1Zonings,
  putV1ZoningsDefaultVersionId,
  getV1ZoningsInfo,
  getV1ZoningsLevel,
  getV1ZoningsList,
  postV1ZoningsListInfo,
  postV1ZoningsListParentCode,
  putV1ZoningsMap,
  postV1ZoningsMap,
  deleteV1ZoningsMap,
  getV1ZoningsMapId,
  getV1ZoningsMyTree,
  getV1ZoningsPath,
  getV1ZoningsSubtreeParentZoningCode,
  getV1ZoningsTree,
  postV1ZoningsUpdown,
  getV1ZoningsVersion,
  putV1ZoningsVersion,
  postV1ZoningsVersion,
  postV1ZoningsVersionCopyVersionId,
  getV1ZoningsVersions,
  getV1ZoningsVersionIdMapList,
  postV2ZoningsExport,
  getV2ZoningsExportTemplate,
  postV2ZoningsImport,
  getV2ZoningsListVersionId,
  getV2ZoningsSearchbyscope,
  getV2ZoningsSearchbyversion,
  getV2ZoningsSubtree,
  getV2ZoningsSubtreeParentZoningCode,
  getV2ZoningsSubtreeVersionIdParentZoningCode,
  getV2ZoningsTreeVersionId,
  putV2ZoningsVersionId,
  postV2ZoningsVersionId,
  deleteV2ZoningsVersionId,
  deleteV2ZoningsVersionIdAll
};
