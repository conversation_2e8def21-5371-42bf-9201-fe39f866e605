<!--
 * @Author: <PERSON><PERSON>
 * @Date: 2024-03-27 15:55:29
 * @LastEditTime: 2024-04-15 11:23:53
 * @LastEditors: Hertz
 * @Description:
-->
<template>
  <div class="base-layout flex flex-col w-full h-full relative">
    <div id="layoutTop" />
    <div class="flex-1 flex">
      <div id="layoutLeft" class="h-full" />
      <div class="flex flex-col flex-1 w-full h-full overflow-hidden z-10">
        <div class="h-full relative">
          <div id="layoutMap" class="h-full w-full" />
          <div id="layoutOverlay" class="absolute pointer-events-none inset-0" />
          <div id="layoutOverlayFull" class="absolute pointer-events-none inset-0" />
        </div>
        <div id="layoutBottom" />
      </div>
      <div id="layoutRight" class="h-full" />
    </div>
    <div
      id="layoutFull"
      class="absolute top-0 left-0 bottom-0 right-0 pointer-events-none z-10"
    />
  </div>
</template>

<script lang="ts" setup>
  const attrs = useAttrs();
  const { openWidget } = useWidget();
  const { subwidgets } = attrs as any;

  async function loadWidget() {
    subwidgets
      && subwidgets.forEach((item: any) => {
        openWidget(item);
      });
  }

  onMounted(async () => {
    loadWidget();
  });
</script>

<style lang="scss" scoped></style>
