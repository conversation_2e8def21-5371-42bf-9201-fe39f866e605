{
  "name": "图源素罐子云快速开发平台", // 系统名称
  "login": {
    "default": "account",
    "list": [
      {
        "label": "账号",
        "key": "account",
        "enable": true,
        "options": {
          "captcha": true,
          "rememberPass": true
        }
      },
      {
        "label": "浙政钉",
        "key": "dingtalk",
        "enable": true,
        "options": {
          "scanUrl": "https://login.dg-work.cn/oauth2/auth.htm?response_type=code&client_id=184_scan_dingoa&redirect_uri=http://192.168.1.184:20100/&scope=get_user_info&authType=QRCODE&embedMode=true",
          "appInfo": {
            "bid": "haiyan-bhgdyjs_zzdpro",
            "sapp_id": "26436",
            "sapp_name": "haiyan-bhgdyjs"
          }
        }
      },
      {
        "label": "企业微信",
        "key": "wework",
        "enable": true,
        "options": {
          "appid": "wwa67d92f82e525b5c",
          "agentid": "1000020"
        }
      }
    ],
    "sso": {
      "collie": {
        "key": "token"
      },
      "nbsypt": {
        "key": "token"
      }
    }
  },
  "theme": {
    "default": {
      "label": "默认主题",
      "algorithm": "default",
      "token": {
        "colorPrimary": "#216BFF",
        "colorInfo": "#216BFF",
        "colorSuccess": "#03CC8F",
        "colorWarning": "#EA6500",
        "colorError": "#F20B00",
        "colorBgBase": "#ffffff",
        "colorTextBase": "#4E5969"
      },
      "activePath": ["/login", "/manage"]
    },
    "dark": {
      "label": "暗黑主题",
      "algorithm": "dark",
      "token": {
        "colorPrimary": "#216BFF",
        "colorInfo": "#216BFF",
        "colorSuccess": "#03CC8F",
        "colorWarning": "#EA6500",
        "colorError": "#F20B00",
        "colorBgBase": "#000000",
        "colorTextBase": "#ffffff"
      },
      "activePath": ["/welcome"]
    }
  },
  // 开发环境配置
  "development": {
    "api": {
      "serverUrl": "http://192.168.1.185:10200",
      "clientUrl": "http://127.0.0.1:3000",
      "list": [
        {
          "name": "collie",
          "path": "/collie"
        },
        {
          "name": "syskeeper",
          "path": "/main/syskeeper"
        }
      ]
    },
    "apps": [
      {
        "label": "系统管理应用",
        "name": "syskeeper",
        "path": "/syskeeper",
        "preload": false,
        "options": {
          "iframe": true,
          "keep-alive": true
        }
      },
      {
        "label": "服务管理应用",
        "name": "mask",
        "path": "/mask",
        "preload": false,
        "options": {
          "iframe": true
        }
      },
      {
        "label": "流程引擎应用",
        "name": "freeflow",
        "path": "/freeflow",
        "preload": false,
        "coverTheme": true,
        "options": {
          "iframe": true
        }
      },
      {
        "label": "开发示例",
        "name": "dimenlink-home",
        "path": "/dimenlink-home",
        "preload": false,
        "options": {
          "iframe": true
        }
      },
      {
        "label": "三维应用",
        "name": "chaos",
        "path": "/chaos",
        "preload": false,
        "options": {
          "iframe": true
        }
      },
      {
        "label": "二维应用",
        "name": "facemap",
        "path": "/facemap",
        "preload": false,
        "options": {
          "iframe": true
        }
      },
      {
        "label": "关系构建应用",
        "name": "plotchain-meta",
        "path": "/plotchain-meta",
        "preload": false,
        "options": {
          "iframe": true
        }
      },
      {
        "label": "指标管理应用",
        "name": "index",
        "path": "/index",
        "preload": false,
        "options": {
          "iframe": true
        }
      },
      {
        "label": "模型管理应用",
        "name": "lego",
        "path": "/lego",
        "preload": false,
        "options": {
          "iframe": true
        }
      },
      {
        "label": "文档管理应用",
        "name": "doc",
        "port": "10200",
        "path": "/main/doc/index.html",
        "preload": false,
        "frame": "wujie",
        "options": {
          "degrade": true
        }
      }
    ]
  },
  // 生产环境配置
  "production": {
    "api": {
      "serverUrl": "http://192.168.1.185:10200",
      "clientUrl": "http://192.168.1.185:10200",
      "list": [
        {
          "name": "collie",
          "path": "/collie"
        },
        {
          "name": "syskeeper",
          "path": "/main/syskeeper"
        }
      ]
    },
    "apps": [
      {
        "label": "系统管理应用",
        "name": "syskeeper",
        "path": "/syskeeper",
        "preload": false,
        "options": {
          "iframe": true,
          "keep-alive": true
        }
      },
      {
        "label": "服务管理应用",
        "name": "mask",
        "path": "/mask",
        "preload": false,
        "options": {
          "iframe": true
        }
      },
      {
        "label": "流程引擎应用",
        "name": "freeflow",
        "path": "/freeflow",
        "preload": false,
        "coverTheme": true,
        "options": {
          "iframe": true
        }
      },
      {
        "label": "开发示例",
        "name": "dimenlink-home",
        "path": "/dimenlink-home",
        "preload": false,
        "options": {
          "iframe": true
        }
      },
      {
        "label": "三维应用",
        "name": "chaos",
        "path": "/chaos",
        "preload": false,
        "options": {
          "iframe": true
        }
      },
      {
        "label": "二维应用",
        "name": "facemap",
        "path": "/facemap",
        "preload": false,
        "options": {
          "iframe": true
        }
      },
      {
        "label": "关系构建应用",
        "name": "plotchain-meta",
        "path": "/plotchain-meta",
        "preload": false,
        "options": {
          "iframe": true
        }
      },
      {
        "label": "指标管理应用",
        "name": "index",
        "path": "/index",
        "preload": false,
        "options": {
          "iframe": true
        }
      },
      {
        "label": "模型管理应用",
        "name": "lego",
        "path": "/lego",
        "preload": false,
        "options": {
          "iframe": true
        }
      },
      {
        "label": "文档管理应用",
        "name": "doc",
        "port": "10200",
        "path": "/main/doc/index.html",
        "preload": false,
        "frame": "wujie",
        "options": {
          "degrade": true
        }
      }
    ]
  }
}