<template>
  <AForm
    ref="formRef"
    layout="horizontal"
    :rules="rules"
    :model="data.formState"
    class="login-form w-full"
    @keyup.enter="handleLogin"
  >
    <AFormItem name="username">
      <AInput v-model:value.trim="data.formState.username" placeholder="登录名" />
    </AFormItem>
    <AFormItem name="password">
      <AInput v-model:value.trim="data.formState.password" type="password" placeholder="密码" />
    </AFormItem>
    <AFormItem v-if="options.captcha" name="captcha">
      <AInput v-model:value="data.formState.captcha" placeholder="验证码" />
      <img
        v-if="data.captchaImg"
        class="captcha-img ml-2xl cursor-pointer"
        :src="data.captchaImg"
        alt="验证码"
        title="点击刷新"
        @click="refreshCaptcha"
      >
    </AFormItem>
    <AFormItem v-if="options.rememberPass">
      <ACheckbox v-model:checked="data.rememberPass">
        记住密码
      </ACheckbox>
    </AFormItem>
    <AFormItem>
      <AButton type="primary" :loading="data.loginLoading" block @click="handleLogin">
        {{ data.loginLoading ? '正在登录......' : '登录' }}
      </AButton>
    </AFormItem>
  </AForm>
</template>

<script setup lang="ts">
  const props = defineProps({
    options: {
      type: Object,
      default: () => ({})
    }
  });
  const route = useRoute();
  const auth = useAuth();
  const { $encrypt } = useNuxtApp();

  const formRef = ref();
  const data = reactive({
    captchaImg: '',
    formState: {
      username: '',
      password: '',
      captcha: ''
    },
    rememberPass: false,
    loginLoading: false
  });
  const rules = {
    username: { required: true, message: '请输入用户名', trigger: 'blur' },
    password: { required: true, message: '请输入密码', trigger: 'blur' },
    captcha: { required: true, message: '请输入验证码', trigger: 'blur' }
  };
  async function refreshCaptcha() {
    const { captcha } = props.options || {};
    if (!captcha)
      return;
    const captchaResult: any = await collieApi.getSsoAuthV1Captcha();
    data.captchaImg = captchaResult?.data;
  }
  function initLoginInfo() {
    const { rememberPass } = props.options || {};
    if ((rememberPass && localStorage.getItem('username')) || localStorage.getItem('password')) {
      data.formState.username = localStorage.getItem('username') || '';
      data.formState.password = localStorage.getItem('password') || '';
      data.rememberPass = true;
    }
  }
  function handleLoginInfo() {
    if (data.rememberPass) {
      const { username, password } = data.formState;
      localStorage.setItem('username', username);
      localStorage.setItem('password', password);
    }
    else {
      localStorage.removeItem('username');
      localStorage.removeItem('password');
    }
  }
  async function getLoginInfo(token: string) {
    const ssoResult = await collieApi.getV1SsoToken(token);
    auth.value = ssoResult as IAuth;
    const { redirect } = route.query;
    const originUrl = (redirect || window.location.origin) as string;
    navigateTo(originUrl, { external: true });
  }
  async function handleLogin() {
    const validateResult = await formRef.value.validate();
    if (!validateResult)
      return;

    const { username, password, captcha } = data.formState;

    const rsapwd: any = $encrypt(password);
    const loginOption = {
      username,
      password: rsapwd,
      captcha
    };
    data.loginLoading = true;
    const loginResult: any = await collieApi.postSsoLogin(loginOption, {
      errorHook: (error: any) => {
        data.loginLoading = false;
        message.error(error.message || '服务器错误');
        refreshCaptcha();
      }
    });
    if (loginResult) {
      data.loginLoading = false;
      handleLoginInfo();
      getLoginInfo(loginResult);
    }
  }
  onMounted(() => {
    initLoginInfo();
  });
  if (import.meta.client)
    refreshCaptcha();
</script>

<style lang="scss" scoped>
.login-form {
  :deep(.ant-input) {
    height: 64px;
    line-height: 1;
    @apply bg-primary bg-opacity-25 border-none text-xl p-2xl;
  }
  :deep(.ant-form-item-control-input-content) {
    @apply flex justify-end;
  }
  :deep(.ant-form-item) {
    margin-bottom: 40px;
  }
  :deep(.ant-form-item-with-help) {
    margin-bottom: 0;
  }
  :deep(.ant-form-item-explain) {
    min-height: 40px;
  }
  :deep(.ant-btn) {
    height: 64px;
    line-height: 1;
    @apply font-bold text-3xl mt-3xl;
  }

  .captcha-img {
    height: 64px;
  }
}
</style>
