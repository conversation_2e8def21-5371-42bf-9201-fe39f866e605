<!--
 * @Author: <PERSON><PERSON>
 * @Date: 2024-02-26 13:28:35
 * @LastEditTime: 2024-05-17 17:12:37
 * @LastEditors: G.TAO
 * @Description:
-->
<template>
  <AppProvider :theme="themeMap[theme]">
    <NuxtLayout>
      <NuxtPage />
    </NuxtLayout>
  </AppProvider>
</template>

<script lang="ts" setup>
  import type { IThemeConfig } from '@tys/nuxt';

  const theme = useTheme();
  const config = useConfig();

  useSeoMeta({
    title: config.value?.name
  });

  const themeMap = computed(() => {
    const themeConfig = (config.value?.theme || {}) as IThemeConfig;
    return themeConfig;
  });
</script>
