{"name": "@tys/nuxt", "type": "module", "version": "1.0.36", "private": false, "description": "基于Nuxt的基础核心模块", "author": "TYS", "keywords": [], "exports": {".": {"types": "./dist/runtime/index.d.ts", "import": "./dist/runtime/index.mjs"}, "./module": {"types": "./dist/types.d.ts", "import": "./dist/module.mjs"}}, "main": "./dist/runtime/index.mjs", "types": "./dist/runtime/index.d.ts", "files": ["dist", "patches"], "engines": {"node": ">=18.20.6"}, "scripts": {"build-lib": "npm run lint && nuxt-module-build build && esbuild --minify src/screen.js --outfile=dist/screen.js", "public": "yarn build-lib && bash ./public.sh", "build": "npm run lint && nuxt build playground", "dev": "npm run lint && nuxt-module-build build --stub && nuxt dev playground", "start": "APP_ROOT_FONT_SIZE=16px node playground/.output/server/index.mjs", "generate": "nuxt generate playground", "preview": "nuxt preview playground", "postinstall": "patch-package && nuxt prepare", "commit": "git cz", "prepare": "husky", "lint": "eslint .", "lint:fix": "eslint . --fix", "tys:api": "tys api -p playground/utils/api -u http://*************:10200 && eslint . --fix", "patch-package": "patch-package @micro-zoe/micro-app"}, "dependencies": {"@ant-design-vue/nuxt": "1.4.6", "@micro-zoe/micro-app": "1.0.0-rc.24", "@nuxt/image": "1.10.0", "@nuxtjs/tailwindcss": "6.13.2", "@types/crypto-js": "4.2.2", "@types/hjson": "2.4.6", "@types/lodash": "4.17.16", "@types/lodash-es": "^4.17.12", "@vueuse/nuxt": "13.0.0", "@wecom/jssdk": "2.2.5", "ant-design-vue": "4.2.6", "crypto-js": "4.2.0", "dayjs": "1.11.13", "element-resize-detector": "1.2.4", "hjson": "3.2.2", "jsencrypt": "3.3.2", "lodash": "4.17.21", "lodash-es": "^4.17.21", "mitt": "3.0.1", "nacos": "2.6.0", "nuxt": "3.16.1", "patch-package": "^8.0.0", "postcss-custom-properties": "14.0.4", "postcss-pxtorem": "6.1.0", "postinstall-postinstall": "^2.1.0", "query-string": "9.1.1", "resize-observer-polyfill": "1.5.1", "vconsole": "^3.15.1", "vite-plugin-svg-icons": "2.0.1", "vite-svg-loader": "5.1.0", "wujie-vue3": "1.0.25"}, "devDependencies": {"@antfu/eslint-config": "^4.10.1", "@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@nuxt/eslint-config": "^1.2.0", "@nuxt/module-builder": "0.8.4", "@nuxtjs/eslint-module": "^4.1.0", "@tys/cli": "^1.0.4", "autoprefixer": "^10.4.18", "commitizen": "^4.3.0", "cz-customizable": "^7.0.0", "eslint": "^9.22.0", "eslint-plugin-format": "^1.0.1", "husky": "^9.0.11", "lint-staged": "^15.2.2", "postcss": "^8.4.37", "prettier": "^3.2.5", "sass": "^1.86.0", "typescript": "5.8.2"}, "config": {"commitizen": {"path": "node_modules/cz-customizable"}, "cz-customizable": {"config": ".cz-config.cjs"}}, "lint-staged": {"*.{js,jsx,ts,tsx,vue}": ["eslint"], "*.{css,scss,json,md}": ["prettier --write"]}, "volta": {"node": "18.20.7"}}