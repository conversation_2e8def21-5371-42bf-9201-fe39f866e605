<template>
  <div
    ref="containerRef"
    class="top-navbar flex-1 flex flex-row items-center overflow-hidden flex-shrink-0"
  >
    <div class="overflow-hidden flex flex-row items-center flex-shrink-0">
      <template v-for="(item, index) in data.visibleButtons" :key="index">
        <div
          class="relative overflow-hidden px-2xl inline-flex flex-shrink-0 items-center justify-center cursor-pointer hover:text-primary"
          :class="{
            'item-selected': data.activeMenuKey === item.url,
            'cursor-not-allowed pointer-events-none text-disabled':
              !item.permCodes.includes('usable'),
          }"
          @click="gotoRoute(item)"
        >
          <span class="text-2xl">{{ item.name }}</span>
        </div>
      </template>
    </div>
    <div v-if="data.hiddenButtons.length">
      <ADropdown placement="bottom">
        <div
          class="flex items-center text-2xl gap-base cursor-pointer"
          :class="{ 'text-primary font-bold': data.selectedHideButton }"
          @click.prevent
        >
          {{ data.selectedHideButton || '更多' }}
          <SvgIcon name="chevron-down" class="w-xl h-xl" />
        </div>
        <template #overlay>
          <AMenu>
            <template v-for="(button, index) in data.hiddenButtons" :key="index">
              <template v-if="button.permCodes.includes('visible')">
                <AMenuItem
                  :key="button.url"
                  :disabled="!button.permCodes.includes('usable')"
                  @click="gotoRoute(button)"
                >
                  <span class="text-2xl">{{ button.name }}</span>
                </AMenuItem>
              </template>
            </template>
          </AMenu>
        </template>
      </ADropdown>
    </div>
  </div>
</template>

<script setup lang="ts">
  const props = defineProps({
    menuList: {
      type: Array as PropType<IResource[]>,
      default: () => []
    }
  });
  const emits = defineEmits(['selected']);
  const { $ResizeObserver } = useNuxtApp();
  const route = useRoute();
  let containerWidth = 300; // 根据实际需求设置容器宽度
  const containerRef = ref<any>(null);
  const data = reactive({
    selectedHideButton: '',
    visibleButtons: props.menuList.filter(
      (it: any) => it.permCodes.includes('visible')
    ) as IResource[],
    hiddenButtons: [] as IResource[],
    activeMenuKey: undefined as string | undefined
  });
  function findParentNode(treeList: IResource[], path: string, parent: IResource | undefined = undefined, index: number = 0): IResource | undefined {
    for (const node of treeList) {
      if (!index)
        parent = node;
      if (node.url === path)
        return parent;

      if (node.children && node.children.length) {
        const result: IResource | undefined = findParentNode(
          node.children,
          path,
          parent,
          index + 1
        );
        if (result)
          return result;
      }
    }
    return undefined;
  }
  function calculateButtonsVisibility() {
    containerWidth = containerRef.value.clientWidth;
    const menuNodes = containerRef.value.children[0]?.children || [];

    let totalWidth = 0;
    const buttonsToShow: any[] = [];
    const visibleDataList = props.menuList.filter(
      (it: any) => it.permCodes.includes('visible')
    );
    for (let i = 0; i < menuNodes.length; i++) {
      const button = menuNodes[i];
      const menuItem = visibleDataList[i];
      if (totalWidth + button.clientWidth <= containerWidth) {
        buttonsToShow.push(menuItem);
        totalWidth += button.clientWidth;
      }
      else {
        break;
      }
    }
    if (buttonsToShow.length < visibleDataList.length) {
      buttonsToShow.pop();
      data.visibleButtons = buttonsToShow;
      data.hiddenButtons = visibleDataList.slice(data.visibleButtons.length - 1);
    }
    else {
      data.visibleButtons = buttonsToShow;
    }
  }
  async function gotoRoute(value: IResource) {
    const fullPath = utils.formatPagePath(route.fullPath);
    if (value.children.length > 0) {
      for (let i = 0; i < value.children.length; i++) {
        const item = value.children[i];
        if (item.permCodes.includes('visible') && item.permCodes.includes('usable')) {
          gotoRoute(item);
          break;
        }
      }
    }
    else if (/https?:\/\//.test(value.url)) {
      window.open(value.url, '_blank');
    }
    else if (fullPath !== value.url) {
      await navigateTo(value.url);
    }
  }

  watch(
    () => route.fullPath,
    (newVar) => {
      const pageState: any = usePage();
      const fullPath = utils.formatPagePath(newVar);
      const resource = pageState.value?.pagePathMap?.[fullPath];
      resource && resource.children && resource.children.length && gotoRoute(resource);

      const path = utils.formatPagePath(route.fullPath);
      const node: IResource | undefined = findParentNode(props.menuList, path);
      emits('selected', node?.children);
      data.activeMenuKey = node?.url;

      if (data.hiddenButtons.findIndex((it: any) => it.url === node?.url) > -1)
        data.selectedHideButton = node?.name || '';
      else
        data.selectedHideButton = '';
    },
    { immediate: true }
  );

  let resizeObserver: any;
  onMounted(() => {
    calculateButtonsVisibility();
    resizeObserver = new $ResizeObserver(() => {
      if (containerRef.value)
        calculateButtonsVisibility();
    });
    resizeObserver.observe(containerRef.value);
  });

  onBeforeUnmount(() => {
    resizeObserver && resizeObserver.unobserve(containerRef.value);
  });
  watch(
    () => props.menuList,
    () => {
      calculateButtonsVisibility();
    }
  );
</script>

<style lang="scss" scoped>
.top-navbar {
  .item-selected {
    @apply text-primary font-bold;
    &:after {
      content: '';
      @apply absolute top-0 bottom-0 left-0 right-0 border-primary border-b-4;
    }
  }
  .hide-button {
    width: 120px;
    overflow: hidden;
    position: relative;
    text-align: center;
    .ant-dropdown-link {
      @apply text-2xl;
    }
    .hide-button-active {
      @apply text-primary font-bold;
    }
  }
}
</style>
