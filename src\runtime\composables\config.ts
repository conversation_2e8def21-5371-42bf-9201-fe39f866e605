/*
 * @Author: Lauxb
 * @Date: 2025-02-19 16:32:40
 * @LastEditTime: 2025-06-30 20:10:17
 * @LastEditors: Lauxb
 * @Description:
 */
import { useState } from 'nuxt/app';

export interface IApi {
  name: string
  serverUrl?: string
  clientUrl?: string
  path?: string
  options?: Record<string, any>
}

export interface IApiConfig {
  serverUrl: string
  clientUrl: string
  wsUrl?: string
  list: IApi[]
}

export interface IApp {
  label: string
  name: string
  url?: string
  protocol?: string
  host?: string
  port?: string
  path?: string
  route?: string
  preload?: boolean
  frame?: string
  options?: any
  data?: any
  coverTheme?: boolean
  params?: any
}

export interface ITheme {
  algorithm: string
  token: object | any
  components?: object | any
  inherit?: boolean
}

export interface IThemeConfig {
  [key: string]: ITheme
}

export interface IConfig {
  name: string
  svg: string
  api: IApiConfig
  apps?: IApp[]
  theme: IThemeConfig
  login: {
    default: string
    list: any[]
    sso: {
      [key: string]: any
    }
  }
  page?: object
  options?: object
}

export function useConfig() {
  return useState('config', () => {
    return undefined as IConfig | undefined;
  });
}
