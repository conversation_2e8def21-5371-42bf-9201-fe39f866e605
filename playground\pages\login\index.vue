<template>
  <div class="login w-full h-full flex flex-col items-center relative">
    <Logo class="absolute top-2xl left-2xl" />
    <div
      class="login-con flex flex-col items-center justify-center h-full absolute right-0 mx-5xl py-5xl"
    >
      <div v-if="loginList.length > 1" class="mode-tabs w-full flex items-center mb-4xl">
        <div
          v-for="(item, index) in loginList"
          :key="index"
          class="tab flex-1 text-center text-4xl cursor-pointer"
          :class="{ 'tab-active': curLogin === item.key }"
          @click="handleSwitchTab(item.key)"
        >
          {{ item.label }}
        </div>
      </div>
      <div class="login-panel w-full flex justify-center">
        <Account v-if="curLogin === 'account'" :options="modeOptions('account')" />
        <Dingtalk v-else-if="curLogin === 'dingtalk'" :options="modeOptions('dingtalk')" />
        <Wework v-else-if="curLogin === 'wework'" :options="modeOptions('wework')" />
        <div v-else class="text-3xl">
          此登录方式暂不支持
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import Account from './components/Account.vue';
  import Dingtalk from './components/Dingtalk.vue';
  import Wework from './components/Wework.vue';

  const config = useConfig();
  const { login } = config.value as IConfig;
  const loginList = ref(login?.list.filter((item: any) => item.enable) || []);

  const curLogin = ref(login.default);

  const modeOptions = computed(() => (key: string) => {
    const loginMode = loginList.value.find((item: any) => key === item.key);
    return loginMode?.options || {};
  });

  function handleSwitchTab(key: string) {
    curLogin.value = key;
  }
</script>

<style lang="scss" scoped>
.login {
  background: url('/image/login-bg.png') no-repeat center;
  background-size: cover;
  .login-con {
    width: 480px;
  }
  .login-panel {
    height: 600px;
  }
  .mode-tabs {
    position: relative;
    height: 64px;
    &::after {
      content: '';
      @apply absolute bottom-0 left-0 w-full h-px;
      background: #979797;
    }
    .tab {
      position: relative;
      height: 64px;
      line-height: 64px;
      &::after {
        content: '';
        left: 50%;
        transform: translate(-50%);
        transition: all ease 0.8s;
        @apply absolute bottom-0 h-xs bg-primary w-0 opacity-0 z-10 rounded-xs;
      }
      &:hover,
      &-active {
        @apply font-bold;
        &::after {
          @apply w-full opacity-100;
        }
      }
    }
  }
}
</style>
