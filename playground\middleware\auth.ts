/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-04-12 16:00:28
 * @LastEditTime: 2025-03-12 15:52:47
 * @LastEditors: Hertz
 * @Description:
 */

export default defineNuxtRouteMiddleware((to) => {
  // eslint-disable-next-line node/prefer-global/process
  if (process.client) {
    const path = utils.formatPagePath(to.fullPath);
    const pageState: any = usePage().value;
    if (!pageState?.pagePathMap[path]?.permCodes?.includes('visible'))
      throw createError({ statusCode: 404, message: ' 此页面不存在' });

    if (!pageState?.pagePathMap[path]?.permCodes?.includes('usable'))
      throw createError({ statusCode: 401, message: ' 此页面没权限' });
  }
});
