/*
 * @Author: Hertz
 * @Date: 2025-03-12 14:55:38
 * @LastEditTime: 2025-05-29 13:32:32
 * @LastEditors: hansenN
 * @Description:
 */
import microApp from '@micro-zoe/micro-app/lib/index.esm.js';
import { useState } from 'nuxt/app';

export interface IResource {
  id: string
  name: string
  type: string
  code: string
  icon: string
  url: string
  content: string
  permCodes: string[]
  children: IResource[]
  opChildren: IResource[]
  options?: any
  parentId: string
}

export interface IPageData {
  pageList: IResource[]
  pageMap: any
  pagePathMap: any
  resourceList: IResource[]
}
function formatPageResource(resources: IResource[], pageMap: any = {}, pagePathMap: any = {}, parentId = '-1') {
  const resourceList: IResource[] = [];
  const pageList: IResource[] = [];
  resources
  && resources.forEach((item: IResource) => {
    const { type, code, content, url, children, id } = item;
    const resourceInfo = formatPageResource(children, pageMap, pagePathMap, id);
    const childList = resourceInfo.resourceList;

    const operateChildren = childList?.filter((ele: any) => ele.type !== 'page') || [];
    const pageChildren = childList?.filter((ele: any) => ele.type === 'page') || [];

    item.options = content ? JSON.parse(content) : {};
    item.parentId = parentId;
    if (type === 'page') {
      item.opChildren = operateChildren;
      item.children = pageChildren;
      pageMap[code] = item;
      pagePathMap[url] = item;
      pageList.push(item);
    }
    resourceList.push(item);
  });
  return {
    resourceList,
    pageList,
    pageMap,
    pagePathMap
  };
}

export const usePage = () => useState('page', () => undefined as IPageData | undefined);

export function formatPagePath(path: string) {
  // microApp 在node环境下会被包在default中，特殊处理
  const microAppRouter = microApp.router || microApp.default?.router;
  const decodePath = microAppRouter.decode(path);
  const { pagePathMap } = usePage().value || {};
  let newPath = '';
  Object.keys(pagePathMap).forEach((key) => {
    if (decodePath.includes(key))
      newPath = key.length > newPath.length ? key : newPath;
  });
  return newPath;
}

export function registerPage(resources: any[]) {
  const pageData = formatPageResource(resources);
  usePage().value = pageData;
}
