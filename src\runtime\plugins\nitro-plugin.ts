import type { NitroAppPlugin } from 'nitropack';
import process from 'node:process';

import { script } from '#screen-script';

export default <NitroAppPlugin> function (nitro) {
  nitro.hooks.hook('render:html', (htmlContext) => {
    let replaceScript = script;
    const appRootClientWidth = process.env.APP_ROOT_CLIENT_WIDTH || '';
    replaceScript = replaceScript.replace('__ROOT_CLIENT_WIDTH__', appRootClientWidth);
    const appRootClientHeight = process.env.APP_ROOT_CLIENT_HEIGHT || '';
    replaceScript = replaceScript.replace('__ROOT_CLIENT_HEIGHT__', appRootClientHeight);
    const appRootFontSize = process.env.APP_ROOT_FONT_SIZE || '';
    replaceScript = replaceScript.replace('__ROOT_FONT_SIZE__', appRootFontSize);
    const appRootMinFontSize = process.env.APP_ROOT_MIN_FONT_SIZE || '';
    replaceScript = replaceScript.replace('__ROOT_MIN_FONT_SIZE__', appRootMinFontSize);
    const appFontSizeScale = process.env.APP_FONT_SIZE_SCALE || '';
    replaceScript = replaceScript.replace('__FONT_SIZE_SCALE__', appFontSizeScale);
    const appFontSizeIsRound = process.env.APP_FONT_SIZE_IS_ROUND || '';
    replaceScript = replaceScript.replace('__FONT_SIZE_IS_ROUND__', appFontSizeIsRound);
    htmlContext.head.push(`<script>${replaceScript}</script>`);

    const baseURL = process.env.NUXT_APP_BASE_URL || '/';
    const faviconLink = `<link rel="icon" type="image/png" href="${baseURL}public/image/logo.png">`;
    htmlContext.head.push(faviconLink);
  });
};
