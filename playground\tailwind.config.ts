/** @type {import('tailwindcss').Config} */

function withOpacity(color: string) {
  return ({ opacityValue }: object | any) => {
    if (opacityValue)
      return `rgba(${color}, ${opacityValue})`;

    return `rgb(${color})`;
  };
}

export default {
  content: ['./playground/**/*.vue', './dist/**/*.vue'],
  theme: {
    fontFamily: {
      pmzd: ['pmzd']
    },
    fontSize: {
      'xs': '10px',
      'sm': '12px',
      'base': '14px',
      'lg': '16px',
      'xl': '18px',
      '2xl': '20px',
      '3xl': '22px',
      '4xl': '24px',
      '5xl': '26px',
      '6xl': '32px',
      '7xl': '48px',
      '8xl': '64px'
    },
    colors: {
      'transparent': 'transparent',
      'current': 'currentColor',
      'black': 'black',
      'white': 'white',
      'primary': withOpacity('var(--primary-color)'),
      'light': withOpacity('var(--light-color)'),
      'link': withOpacity('var(--link-color)'),
      'info': withOpacity('var(--info-color)'),
      'success': withOpacity('var(--success-color)'),
      'warning': withOpacity('var(--warning-color)'),
      'error': withOpacity('var(--error-color)'),
      'heading': withOpacity('var(--heading-color)'),
      'content': withOpacity('var(--content-color)'),
      'content-secondary': withOpacity('var(--content-secondary-color)'),
      'disabled': withOpacity('var(--disabled-color)')
    },
    backgroundColor: (theme: any) => ({
      ...theme('colors'),
      DEFAULT: withOpacity('var(--background-color)')
    }),
    borderColor: (theme: any) => ({
      ...theme('colors'),
      DEFAULT: withOpacity('var(--border-color)')
    }),
    divideColor: (theme: any) => ({
      ...theme('colors'),
      DEFAULT: withOpacity('var(--divider-color)')
    }),
    spacing: {
      '0': '0px',
      'auto': 'auto',
      'px': '1px',
      'xs': '2px',
      'sm': '4px',
      'base': '8px',
      'lg': '12px',
      'xl': '16px',
      '2xl': '24px',
      '3xl': '32px',
      '4xl': '64px',
      '5xl': '128px'
    },
    borderRadius: {
      DEFAULT: '4px',
      none: '0px',
      xs: '1px',
      sm: '2px',
      base: '4px',
      lg: '8px',
      xl: '16px',
      full: '9999px'
    },
    borderWidth: {
      DEFAULT: '1px',
      0: '0px',
      2: '2px',
      4: '4px',
      6: '6px',
      8: '8px'
    },
    divideWidth: {
      DEFAULT: '1px',
      0: '0px',
      2: '2px',
      4: '4px',
      6: '6px',
      8: '8px'
    },
    boxShadow: {
      DEFAULT: 'var(--box-shadow)',
      none: 'none'
    },
    extend: {}
  },
  plugins: []
};
