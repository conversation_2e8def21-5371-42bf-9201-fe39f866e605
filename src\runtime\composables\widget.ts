import { ConfigProvider as AConfigProvider, Style<PERSON>rovider as AStyleProvider, px2remTransformer } from 'ant-design-vue';
import zhCN from 'ant-design-vue/es/locale/zh_CN.js';
import { useNuxtApp, useRuntimeConfig } from 'nuxt/app';
import { createVNode, h, render } from 'vue';

const px2remOptions: any = { rootValue: 20, precision: 2, mediaQuery: false };

let widgetResourceMap: any = {};
const widgetComponentMap: any = {};
const activeWidgetMap: any = {};

function createWidget(component: any, options: any) {
  const vNode: any = createVNode({ ...component, inheritAttrs: false }, { ...options });
  const app = useNuxtApp().vueApp;
  const getPopupContainer = (triggerNode: any) => {
    if (triggerNode)
      return triggerNode.parentNode;
    const runtimeConfig = useRuntimeConfig();
    const popupContainer: string = (options.popupContainer || runtimeConfig.public.appId || 'app') as string;
    return document.getElementById(popupContainer);
  };
  const widgetProvider: any = h(
    AConfigProvider,
    {
      locale: zhCN,
      getPopupContainer
    },
    { default: () => [
      h(
        AStyleProvider,
        {
          hashPriority: 'high',
          transformers: [px2remTransformer(px2remOptions)]
        },
        { default: () => vNode }
      )
    ] }
  );
  widgetProvider.appContext = app._context;
  widgetProvider.mount = (el: any) => {
    const container = document.createElement('div');
    render(widgetProvider, container);
    const nodeEl = vNode.el;
    if (nodeEl.className?.indexOf('h-full') > -1)
      container.classList.add('h-full');

    if (nodeEl.className?.indexOf('w-full') > -1)
      container.classList.add('w-full');

    el.appendChild(container);
  };
  widgetProvider.unmount = () => {
    const { parentNode } = widgetProvider.el;
    render(null, parentNode);
    parentNode.parentNode.removeChild(parentNode);
  };
  return widgetProvider;
}

function matchContainer(selector: any) {
  return document.getElementById(selector);
}

function formatResource(resources: any, resourceMap: any = {}, ext: any = {}) {
  const resourceList: any[] = [];

  resources.forEach((item: any) => {
    const { id, content, children } = item;
    const config = JSON.parse(content);
    const resource = {
      ...item,
      ...config,
      ...ext,
      subwidgets: []
    };
    if (children && children.length > 0) {
      const operateChildren = children?.filter((ele: any) => ele.type !== 'component') || [];
      const widgetChildren = children?.filter((ele: any) => ele.type === 'component') || [];

      resource.operates = operateChildren;

      // 包含所有的子微件
      resource.allSubwidgets = formatResource(widgetChildren, resourceMap, ext).resourceList;

      // 只包括可见子的微件
      resource.subwidgets = resource.allSubwidgets.filter(
        (subwidget: any) => subwidget.permCodes.includes('visible')
      );
    }
    delete resource.children;
    if (resource.meta) {
      resource.meta.matchContainer = matchContainer;
      resourceMap[id] = resource;
      resourceList.push(resource);
    }
    else {
      console.log('组件配置错误：', resource);
    }
  });
  return { resourceMap, resourceList };
}

function registerWidget(modules: any, resources: any[], options: any = {}) {
  Object.keys(modules).forEach((key) => {
    // eslint-disable-next-line regexp/no-super-linear-backtracking
    const moduleName = key.match(/^.*\/(.+)\/index.vue$/)?.[1] || '';
    widgetComponentMap[moduleName] = modules[key];
  });
  const { resourceMap, resourceList } = formatResource(resources, {}, options);
  widgetResourceMap = resourceMap;
  return resourceList;
}

function closeWidget(id: string) {
  const widget = activeWidgetMap[id];
  if (widget) {
    widget.descriptor?.unmount?.();
    widget.descriptor.$el
    && widget.descriptor.$el?.parentElement?.removeChild(widget.descriptor.$el);
    widget.descriptor._container
    && widget.descriptor._container?.parentElement?.removeChild(widget.descriptor._container);
    delete activeWidgetMap[id];
    widget.subwidgets
    && widget.subwidgets.forEach((item: any) => {
      closeWidget(item.id);
    });
  }
  else {
    console.log('Widget', `${id}组件未运行`);
  }
}

function closeAllWidget() {
  Object.keys(activeWidgetMap).forEach((key: string) => {
    closeWidget(key);
  });
}

async function openWidget(widgetInfo: any) {
  const { id, code, meta, permCodes } = widgetInfo;
  const component = widgetComponentMap[code];
  if (!component) {
    console.log('Widget', `${code}组件不存在`);
    return;
  }
  if (!permCodes.includes('usable')) {
    console.log('Widget', `${code}该组件没有权限！`);
    return;
  }

  let descriptor: any;
  if (meta.frame) {
    const panelComonent = widgetComponentMap[meta.frame] || widgetComponentMap.Panel;
    const compConfig = await panelComonent();
    descriptor = createWidget(compConfig.default, { ...widgetInfo, component });
  }
  else {
    const compConfig = await component();
    descriptor = createWidget(compConfig.default, { ...widgetInfo });
  }
  if (descriptor?.mount) {
    let { container } = meta;
    if (meta.matchContainer)
      container = meta.matchContainer(meta.container, meta.className);

    descriptor?.mount?.(container);
  }
  activeWidgetMap[id] = { ...widgetInfo, descriptor };
  if (meta.doOnce)
    closeWidget(id);
}

function getActiveWidgetMap() {
  return activeWidgetMap;
}

function getWidgetById(id: string) {
  return widgetResourceMap[id];
}

const mapInstanceMap: Map<string, any> = new Map();

function setMap(id: string, map: any) {
  mapInstanceMap.set(id, map);
};

function getMap(id: string) {
  return mapInstanceMap.get(id);
};

function removeMap(id: string) {
  mapInstanceMap.delete(id);
};

function removeAllMap() {
  mapInstanceMap.clear();
};

export function useWidget() {
  return {
    registerWidget,
    openWidget,
    closeWidget,
    closeAllWidget,
    getActiveWidgetMap,
    getWidgetById,
    setMap,
    getMap,
    removeMap,
    removeAllMap
  };
}
