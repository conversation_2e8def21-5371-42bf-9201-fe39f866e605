#!/bin/bash
###
 # @Author: <PERSON>tz
 # @Date: 2025-01-16 13:53:15
 # @LastEditTime: 2025-03-15 12:27:36
 # @LastEditors: Hertz
 # @Description: 
### 

set -e

read -p "输入版本号[x.x.x] > " version
vformat='^([0-9]+\.){2}(\*|[0-9]+)$'
while :
do
  if [[ $version =~ $vformat ]]; then
    break
  else
    read -p "格式有误，请重新输入版本号[x.x.x]  > " version
  fi
done

node ./update-version.cjs $version

libDir="lib"

if [ -d "$libDir" ]; then
    rm -r "$libDir"
fi

mkdir "$libDir"

cp ./package.json ./lib/package.json

cp ./README.md ./lib/README.md

cp -r ./dist ./lib/dist

cp -r ./patches ./lib/patches

node ./update-package.cjs

cd ./lib

npm unpublish --force --registry http://*************:44873

npm publish --registry http://*************:44873

cd ../
