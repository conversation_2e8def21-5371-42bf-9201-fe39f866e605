import type { IApi, IApiConfig } from './config';
import { useFetch, useNuxtApp, useRuntimeConfig } from 'nuxt/app';
import { $fetch } from 'ofetch';

export interface UseFetchOptions<DataT = any> {
  key?: string
  method?: string
  query?: Record<string, any>
  params?: Record<string, any>
  body?: Record<string, any>
  headers?: Record<string, string> | [key: string, value: string][] | Headers
  baseURL?: string
  server?: boolean
  lazy?: boolean
  immediate?: boolean
  getCachedData?: (key: string) => DataT
  deep?: boolean
  default?: () => DataT
  transform?: (input: DataT) => DataT
  pick?: string[]
  watch?: any
  errorHook?: (params: any) => void
  timeout?: number
  responseType?: 'json' | 'text' | 'blob' | 'arraybuffer' | 'formData'
}

export type OptionsType = Partial<UseFetchOptions<any>>;

enum RequestMethod {
  GET = 'GET',
  POST = 'POST',
  PUT = 'PUT',
  DEL = 'DELETE'
}

let defaultApiOptions: Record<string, any>;
const apiMap = {} as Record<string, IApi>;
const requestQueue: Map<string, any> = new Map();

function checkResultHasData(result: any) {
  if (typeof result === 'object' && result != null && 'data' in result) {
    return true;
  }
  return false;
}

function isPlainObject(value: any) {
  return typeof value === 'object' && value !== null && Object.prototype.toString.call(value) === '[object Object]';
}

function deepMerge(...objects: any) {
  const result: any = {};

  objects.forEach((obj: any) => {
    if (isPlainObject(obj)) {
      Object.keys(obj).forEach((key) => {
        const value = obj[key];
        if (isPlainObject(value)) {
          if (isPlainObject(result[key])) {
            result[key] = deepMerge(result[key], value);
          }
          else {
            result[key] = { ...value };
          }
        }
        else {
          result[key] = value;
        }
      });
    }
  });

  return result;
}

export function getClientUrl(url: string | any) {
  if (url) {
    return url.replace('{protocol}', window.location.protocol).replace('{hostname}', window.location.hostname).replace('{port}', window.location.port);
  }
  return url;
}

export function useRequest() {
  const registerApi = (apiConfig: IApiConfig, options: Record<string, any>) => {
    defaultApiOptions = options || {};
    apiConfig.list.forEach((api: IApi) => {
      const key = api.name;
      !api.clientUrl && (api.clientUrl = apiConfig.clientUrl);
      !api.serverUrl && (api.serverUrl = apiConfig.serverUrl);
      apiMap[key] = api;
    });
  };

  const getApiBaseUrl = (key: keyof IApiConfig): string | undefined => (import.meta.client ? getClientUrl(`${apiMap[key]?.clientUrl}`) : `${apiMap[key]?.serverUrl}`) + (apiMap[key]?.path || '');

  const request = async (
    method: RequestMethod,
    api: IApi,
    url: string,
    options: OptionsType
  ): Promise<any> => {
    const controller: AbortController = new AbortController();
    const { signal } = controller;
    const key: string = `${method}:${url}:${JSON.stringify(options.body || {})}:${JSON.stringify(options.params || {})}`;
    requestQueue.set(key, controller);
    let apiOptions: any = {};
    const runtimeConfig = useRuntimeConfig();
    let baseURL: string = runtimeConfig.app.baseURL; // import.meta.env.BASE_URL.replace(/_nuxt\//g, '');
    if (api) {
      apiOptions = api.options || {};

      baseURL = (import.meta.client ? getClientUrl(api.clientUrl) : api.serverUrl) + (api.path || '');
    }
    const mergedOptions = deepMerge(defaultApiOptions, apiOptions, options);

    if (import.meta.client) {
      const nuxtApp = useNuxtApp();
      if (!nuxtApp.payload.data[key]) {
        try {
          const result = await $fetch(url, {
            method,
            baseURL,
            signal,
            key,
            credentials: 'include',
            ...mergedOptions
          });
          requestQueue.delete(key);
          return checkResultHasData(result) ? result.data : result;
        }
        catch (error: any) {
          requestQueue.delete(key);
          const errorData = error.data || {
            status: error.value.statusCode,
            message: error.value.statusMessage,
            url
          };
          const errorHook = options?.errorHook || apiOptions?.errorHook || defaultApiOptions?.errorHook;
          if (errorHook)
            errorHook(errorData);
          else
            console.log(`API请求错误${url}`, errorData);

          return null;
        }
      }
    }
    const { data, error }: any = await useFetch(url, {
      method,
      baseURL,
      signal,
      key,
      credentials: 'include',
      ...mergedOptions
    } as any).catch(() => console.log('error'));
    requestQueue.delete(key);
    if (error.value) {
      const errorData = error.value.data || {
        status: error.value.statusCode,
        message: error.value.statusMessage,
        url
      };
      const errorHook = options?.errorHook || apiOptions?.errorHook || defaultApiOptions?.errorHook;
      if (errorHook)
        errorHook(errorData);
      else
        console.log(`API请求错误${url}`, errorData);

      return null;
    }
    return checkResultHasData(data.value) ? data.value.data : data.value;
  };

  const cancelAllRequests = () => {
    requestQueue.forEach(item => item.abort());
    requestQueue.clear();
  };

  const cancelRequest = (requestKey: string) => {
    const requestItem = requestQueue.get(requestKey);
    if (requestItem) {
      requestItem.abort();
      requestQueue.delete(requestKey);
    }
  };

  const cancelRequests = ([...requestKeys]: string[]) => {
    requestKeys.forEach((key) => {
      cancelRequest(key);
    });
  };

  const get = (apiKey: string, url: string, options: OptionsType = {}) => {
    return request(RequestMethod.GET, apiMap[apiKey], url, options);
  };

  const post = (apiKey: string, url: string, options: OptionsType = {}) => {
    return request(RequestMethod.POST, apiMap[apiKey], url, options);
  };

  const put = (apiKey: string, url: string, options: OptionsType = {}) => {
    return request(RequestMethod.PUT, apiMap[apiKey], url, options);
  };

  const del = (apiKey: string, url: string, options: OptionsType = {}) => {
    return request(RequestMethod.DEL, apiMap[apiKey], url, options);
  };

  return {
    registerApi,
    getApiBaseUrl,
    request,
    get,
    post,
    put,
    del,
    cancelAllRequests,
    cancelRequest,
    cancelRequests
  };
}
