import type { IConfig, IThemeConfig } from '@tys/nuxt';

export default defineNuxtPlugin(async () => {
  const theme = useTheme();
  const config = useConfig();
  const route = useRoute();
  const auth = useAuth();

  // 获取应用配置
  const configData: any = await appApi.getAppConfig();
  if (configData) {
    const envMode: string = import.meta.env.MODE;
    config.value = {
      name: configData.name,
      theme: configData.theme,
      login: configData.login,
      ...configData[envMode]
    } as IConfig;
  }

  // 设置默认主题
  const themeConfig = (config.value?.theme || {}) as IThemeConfig;
  const themeValue: string = route.query.theme as string;
  if (themeValue && themeConfig[themeValue]) {
    theme.value = themeValue;
  }
  else {
    Object.keys(themeConfig).forEach((key) => {
      const themeItem: any = themeConfig[key];
      if (themeItem.activePath.find((value: any) => route.path.includes(value)))
        theme.value = key;
    });
  }

  // 注册 API
  const { registerApi } = useRequest();
  registerApi(config.value?.api as IApiConfig, {
    errorHook: (error: any) => {
      const { status, message } = error;
      if (status === 401 && route.path !== '/login')
        throw createError({ statusCode: 401, message: message || '当前系统未登录' });

      if (status === 402)
        throw createError({ statusCode: 402, message: message || '当前系统还未进行授权' });

      if (status === 404)
        message.error(message || '此页面不存在!');

      if (status === 429)
        message.error(message || '当前系统使用人数较多,请稍后再试!');
    }
  });

  // 获取 SSO 信息
  const source = route.query.source as string;
  const loginSso: any = config.value?.login?.sso || {};
  if (source && loginSso[source]) {
    const paramsValue = route.query[loginSso[source].key];
    let ssoResult: any;
    if (source === 'collie')
      ssoResult = await collieApi.getV1SsoToken(paramsValue as string);
    else if (source === 'nbsypt')
      ssoResult = await collieApi.postV1NbsyptSso({ token: paramsValue });

    if (ssoResult) {
      auth.value = ssoResult as IAuth;
      console.log(`根据${source}的${loginSso[source].key}获取SSO成功`);
    }
    else {
      console.log(`根据${source}的${loginSso[source].key}获取SSO失败，请检查配置登录方法或传入参数是否为空`);
    }
  }
  else {
    const ssoResult = await collieApi.getV1Sso();
    if (ssoResult) {
      auth.value = ssoResult as IAuth;
      console.log('获取SSO成功');
    }
  }
  // 获取应用资源
  const pageResult = await syskeeperApi.getV1AuthorizationMyPage({});
  registerPage((pageResult || []) as IResource[]);
  console.log('资源获取成功');

  if (import.meta.client) {
    // 监听子应用事件
    const windowInstance = window as any;
    windowInstance.$wujie?.bus.$on('themeChange', (value: string) => {
      theme.value = value;
    });
    windowInstance.microApp?.addDataListener((data: any) => {
      if (data.theme)
        theme.value = data.theme;
    });
  }
});
