/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-02-27 10:13:58
 * @LastEditTime: 2025-04-23 14:18:40
 * @LastEditors: Hertz
 * @Description: 读取应用配置
 */

import fs from 'node:fs';
import process from 'node:process';
import { define<PERSON><PERSON><PERSON><PERSON><PERSON> } from 'h3';
import hjson from 'hjson';
import lodash from 'lodash';
import { NacosConfigClient } from 'nacos';
import cryptoUtil from '../../utils/crypto';

function getPublicConfig() {
  const envMap = process.env;
  let rootPath = '.';
  if (envMap.NUXT_VITE_NODE_OPTIONS) {
    const nuxtViteNodeOptions = JSON.parse(envMap.NUXT_VITE_NODE_OPTIONS);
    rootPath = nuxtViteNodeOptions.root;
  }
  const configStr: any = fs.readFileSync(`${rootPath}/public/config/app.hjson`);
  return configStr.toString();
}

let configClient: any = null;
if (
  process.env.NACOS_HOST
  && process.env.NACOS_PORT
  && process.env.NACOS_USERNAME
  && process.env.NACOS_PASS
  && process.env.NACOS_NAMESPACE
) {
  console.log('读取环境变量');
  const pass = cryptoUtil.Decrypt(process.env.NACOS_PASS || '');
  configClient = new NacosConfigClient({
    serverAddr: `${process.env.NACOS_HOST}:${process.env.NACOS_PORT}`,
    username: process.env.NACOS_USERNAME,
    password: pass,
    namespace: process.env.NACOS_NAMESPACE,
    requestTimeout: 6000
  });
}
else {
  console.log(' Nacos变量配置不全，将会使用本地配置');
}

// configClient.subscribe(
//   {
//     dataId: 'collie-web.json',
//     group: 'DEFAULT_GROUP'
//   },
//   (content: any) => {
//     console.log(content);
//   }
// );

export default defineEventHandler(async () => {
  let content: any;
  try {
    if (configClient) {
      content = await configClient.getConfig(
        process.env.NACOS_DATA_ID || 'web.json',
        process.env.NACOS_GROUP || 'DEFAULT_GROUP'
      );
      console.log('获取Nacos配置成功');
    }
    else {
      content = getPublicConfig();
      console.log('获取本地配置成功');
    }
    if (lodash.isString(content))
      content = hjson.parse(content);
  }
  catch (error) {
    console.log('获取配置失败', error);
  }
  return content;
});
