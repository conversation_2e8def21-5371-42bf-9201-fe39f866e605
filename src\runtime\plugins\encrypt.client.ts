/*
 * @Author: <PERSON><PERSON>
 * @Date: 2025-01-16 13:53:15
 * @LastEditTime: 2025-04-23 14:04:30
 * @LastEditors: Hertz
 * @Description:
 */
import { JSEncrypt } from 'jsencrypt';
import { defineNuxtPlugin } from 'nuxt/app';

export default defineNuxtPlugin(() => {
  const encrypt = (text: string, key = '') => {
    const jsencrypt = new JSEncrypt();
    const defaultKey = 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDBOx23u3INdVTaW70Z380AHIZMjr9Ibd5bK7NrC1sR1LcM8U+k4/PyNhCJymrksVoaH7fGFALlj3ewwkDJD4J/ZWps0nBqNqWKutaPKfFHLxveywFBl2ZFQ1btYhrIAE9/DDt8+eGfP6SrCZqB7OZBH1gNMv17n4SuahZK+FIEgQIDAQAB';
    jsencrypt.setPublicKey(key || defaultKey);
    return jsencrypt?.encrypt(text);
  };
  return {
    provide: {
      encrypt
    }
  };
});
