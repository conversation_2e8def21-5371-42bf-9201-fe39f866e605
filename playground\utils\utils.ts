/*
 * @Author: <PERSON><PERSON>
 * @Date: 2025-01-20 15:07:31
 * @LastEditTime: 2025-03-15 13:47:11
 * @LastEditors: Hertz
 * @Description:
 */
import microApp from '@micro-zoe/micro-app/lib/index.esm.js';

const encode = (str: string = '') => microApp.router.encode(str);

const decode = (str: string = '') => microApp.router.decode(str);

function formatPagePath(path: string) {
  const decodePath = decode(path);
  const { pagePathMap } = usePage().value || {};
  let newPath = '';
  Object.keys(pagePathMap).forEach((key) => {
    if (decodePath.includes(key))
      newPath = key.length > newPath.length ? key : newPath;
  });
  return newPath;
}

export default {
  decode,
  encode,
  formatPagePath
};
