html,
body {
  font-family: 'Microsoft YaHei', 'PingFang SC';
  line-height: 1.15;
  width: 100%;
  height: 100%;
  font-size: 16px;
}
#__nuxt {
  width: 100%;
  height: 100%;
}

/* 重写滚动条样式 */

/* 定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸 */

::-webkit-scrollbar {
  width: 5px;
  height: 6px;
  background-color: transparent;
}

/* 定义滚动条轨道 内阴影+圆角 */

::-webkit-scrollbar-track {
  width: 0;
  background-color: transparent;
  border-radius: 0;
}

/* 定义滑块 内阴影+圆角 */

::-webkit-scrollbar-thumb {
  width: 6px;
  border-radius: 6px;
  box-shadow: inset 0 0 6px var(--tailwind-content-secondary-color);
}

/* 滑块效果 */

::-webkit-scrollbar-thumb:hover {
  border-radius: 6px;
  box-shadow: inset 0 0 6px var(--tailwind-content-color);
}
