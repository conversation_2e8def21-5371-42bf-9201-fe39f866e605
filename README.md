# @tys/nuxt

基于Nuxt的基础核心模块

## 快速开始

### 1、添加@tys/nuxt到应用工程

```bash
yarn add -D @tys/nuxt
```

### 2、在nuxt.config.ts中添加@tys/nuxt模块

```ts
// nuxt.config.ts
export default defineNuxtConfig({
  modules: ['@tys/nuxt/module'],
  tys: {
    // 忽略加载的插件 ignorePlugins: ['microApp.client']
    ignorePlugins: []
  }
});
```

### 3、所需组件及工具类需要显式引入方式

```vue
<script lang="ts" setup>
  import { useTheme } from '@tys/nuxt';
  const theme = useTheme();
  console.log(theme.value);
</script>
```

### 4、为应对@micro-zoe/micro-app新版本无法在node环境运行的问题，使用patch-package打补丁

由于只对'@micro-zoe/micro-app/lib/index.esm.js'文件打了补丁所以后续不管是库还是项目上使用@micro-zoe/micro-app时，都需要导入到@micro-zoe/micro-app/lib/index.esm.js文件，而不是'@micro-zoe/micro-app'

同时项目上需要在`package.json`中的`postinstall`中配置`patch-package --patch-dir ./node_modules/@tys/nuxt/patches`命令
