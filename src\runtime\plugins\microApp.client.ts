import microApp from '@micro-zoe/micro-app/lib/index.esm.js';
import { defineNuxtPlugin, useRuntimeConfig } from 'nuxt/app';
import WujieVue from 'wujie-vue3';
import { useConfig } from '../composables/config';

export default defineNuxtPlugin((nuxtApp) => {
  nuxtApp.vueApp.use(WujieVue);
  const config = useConfig();
  const runtimeConfig = useRuntimeConfig();
  const preloadApps: any = [];
  (config.value?.apps || []).forEach((app) => {
    const { url, protocol, host, port, path } = app || {};
    const baseUrl = url || `${protocol || window.location.protocol}//${host || window.location.hostname}:${port || window.location.port}${path || ''}`;
    if (app.preload) {
      const appInfo = {
        name: app.name,
        url: baseUrl,
        ...app.options || {},
        attrs: { src: `${runtimeConfig.app.baseURL}app.html` }
      };
      if (app.frame === 'wujie')
        WujieVue.preloadApp(appInfo);
      else
        preloadApps.push(appInfo);
    }
  });
  microApp.start({
    iframeSrc: `${runtimeConfig.app.baseURL}app.html`
  });

  microApp.preFetch(preloadApps, 5000);
  return {
    provide: {
      wujie: WujieVue,
      microApp
    }
  };
});
