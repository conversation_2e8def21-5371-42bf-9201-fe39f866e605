<template>
  <BaseLayout :is-show-sider="data.submenuData.length > 0">
    <template #Header>
      <div class="w-full h-full flex justify-between items-center pl-3xl pr-3xl">
        <Logo />
        <TopNavbar class="flex-1 mx-3xl" :menu-list="data.menuData" @selected="handleMenuSelect" />
        <User />
      </div>
    </template>
    <template #Sider>
      <LeftNavbar v-show="data.submenuData.length > 0" :menu-list="data.submenuData" />
    </template>
    <template #Content>
      <div
        class="w-full h-full rounded overflow-hidden relative"
        :class="{ 'px-xl py-lg': data.submenuData.length }"
        style="background-clip: content-box"
      >
        <slot />
      </div>
    </template>
  </BaseLayout>
</template>

<script lang="ts" setup>
  const { pageMap } = usePage().value || {};
  const data = reactive({
    menuData: [],
    submenuData: []
  });
  const route = useRoute();
  const { layout } = route.meta;
  const { children } = pageMap[layout] || {};
  data.menuData = children || [];
  function handleMenuSelect(value: any) {
    data.submenuData = value || [];
  }
</script>

<style></style>
