<template>
  <ALayout class="w-full h-full overflow-hidden relative">
    <ALayoutHeader>
      <slot name="Header" />
    </ALayoutHeader>
    <ALayout :has-sider="isShowSider">
      <ALayoutSider v-show="isShowSider" v-model:collapsed="data.collapsed" :collapsible="true">
        <template #trigger>
          <div class="trigger-wrapper" :title="data.collapsed ? '展开' : '收起'">
            <SvgIcon v-if="data.collapsed" name="menu-unfold-1" />
            <SvgIcon v-else name="menu-fold-1" />
          </div>
        </template>
        <slot name="Sider" />
      </ALayoutSider>
      <ALayoutContent>
        <slot name="Content" />
      </ALayoutContent>
    </ALayout>
    <ALayoutFooter>
      <slot name="Footer" />
    </ALayoutFooter>
  </ALayout>
</template>

<script lang="ts" setup>
  defineProps({
    isShowSider: {
      type: Boolean,
      default: false
    }
  });
  const data = reactive({
    collapsed: false
  });
</script>

<style lang="scss" scoped>
.trigger-wrapper {
  @apply w-full h-full text-content cursor-pointer;
  &:hover {
    @apply text-primary;
  }
}
.ant-layout {
  .ant-layout-header {
    @apply bg border-b p-0;
  }
  .ant-layout-footer {
    @apply bg p-0;
  }
  .ant-layout-content {
    @apply bg-content bg-opacity-[0.005];
  }
  .ant-layout-sider {
    flex: 0 0 230px !important;
    max-width: 230px !important;
    min-width: 230px !important;
    width: 230px !important;
    @apply bg;
    :deep(.ant-layout-sider-trigger) {
      width: 230px !important;
      @apply bg;
    }
  }
  .ant-layout-sider-collapsed {
    flex: 0 0 82px !important;
    max-width: 82px !important;
    min-width: 82px !important;
    width: 82px !important;
    @apply bg;
    :deep(.ant-layout-sider-trigger) {
      width: 82px !important;
    }
  }
}
</style>
