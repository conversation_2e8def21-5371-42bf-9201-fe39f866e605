const apiName = 'syskeeper';

const { get, post, put, del } = useRequest();

function getHeaders() {
  const cookies = getCookies();
  if (cookies)
    return { Cookie: cookies };
  return {} as any;
};

/**
 * @description 获取应用列表
 * @param params 参数 {
 *   pageindex： integer
 *   pagesize： integer
 *   keyword?： string
 *   icon?： boolean
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1Applets(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/applets', { headers: getHeaders(), ...options, params });
}

/**
 * @description 新建应用
 * @param params 参数 {
 *   completed?： integer
 *   deleteIcon?： boolean
 *   haveTitle?： integer
 *   mainOrg： string
 *   name： string
 *   remark?： string
 *   stage?： integer
 *   type?： string
 * }
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1Applets(params: Record<string, any>, body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/applets', { body, headers: getHeaders(), ...options, params });
}

/**
 * @description 物理删除应用（传入应用id集合）
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function deleteV1Applets(body: Record<string, any>, options: OptionsType = {}) {
  return del(apiName, '/v1/applets', { body, headers: getHeaders(), ...options });
}

/**
 * @description 复制应用
 * @param appletId string
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1AppletsCopyAppletId(appletId: string, options: OptionsType = {}) {
  return post(apiName, `/v1/applets/copy/${appletId}`, { headers: getHeaders(), ...options });
}

/**
 * @description 查看应用图标base64编码
 * @param appletId string
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1AppletsIconBase64AppletId(appletId: string, options: OptionsType = {}) {
  return get(apiName, `/v1/applets/icon/base64/${appletId}`, { headers: getHeaders(), ...options });
}

/**
 * @description 查看应用图标（已过时，切换到接口icon/base64/{appletId}）
 * @param appletId string
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1AppletsIconAppletId(appletId: string, options: OptionsType = {}) {
  return get(apiName, `/v1/applets/icon/${appletId}`, { headers: getHeaders(), ...options });
}

/**
 * @description 根据应用id获取应用元数据信息
 * @param appletId string
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1AppletsAppletId(appletId: string, options: OptionsType = {}) {
  return get(apiName, `/v1/applets/${appletId}`, { headers: getHeaders(), ...options });
}

/**
 * @description 更新应用
 * @param appletId string
 * @param params 参数 {
 *   completed?： integer
 *   deleteIcon?： boolean
 *   haveTitle?： integer
 *   mainOrg： string
 *   name： string
 *   remark?： string
 *   stage?： integer
 *   type?： string
 * }
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function putV1AppletsAppletId(appletId: string, params: Record<string, any>, body: Record<string, any>, options: OptionsType = {}) {
  return put(apiName, `/v1/applets/${appletId}`, { body, headers: getHeaders(), ...options, params });
}

/**
 * @description 获取应用下的组件信息
 * @param appletId string
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1AppletsAppletIdComponent(appletId: string, options: OptionsType = {}) {
  return get(apiName, `/v1/applets/${appletId}/component`, { headers: getHeaders(), ...options });
}

/**
 * @description 保存应用与组件关联（已过时，建议使用：applets/v2/{appletId}/components）
 * @param appletId string
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1AppletsAppletIdComponent(appletId: string, body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, `/v1/applets/${appletId}/component`, { body, headers: getHeaders(), ...options });
}

/**
 * @description 更新应用状态
 * @param appletId string
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function putV1AppletsAppletIdStatus(appletId: string, body: Record<string, any>, options: OptionsType = {}) {
  return put(apiName, `/v1/applets/${appletId}/status`, { body, headers: getHeaders(), ...options });
}

/**
 * @description 获取应用下组件配置内容
 * @param appletId string
 * @param componentId string
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1AppletsAppletIdComponentIdProfile(appletId: string, componentId: string, options: OptionsType = {}) {
  return get(apiName, `/v1/applets/${appletId}/${componentId}/profile`, { headers: getHeaders(), ...options });
}

/**
 * @description 保存应用下组件配置内容
 * @param appletId string
 * @param componentId string
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1AppletsAppletIdComponentIdProfile(appletId: string, componentId: string, body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, `/v1/applets/${appletId}/${componentId}/profile`, { body, headers: getHeaders(), ...options });
}

/**
 * @description 保存应用与组件关联
 * @param appletId string
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV2AppletsAppletIdComponents(appletId: string, body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, `/v2/applets/${appletId}/components`, { body, headers: getHeaders(), ...options });
}

/**
 * @description 清理授权里面的脏数据
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1AuthorizationClearDirty(options: OptionsType = {}) {
  return get(apiName, '/v1/authorization/clear/dirty', { headers: getHeaders(), ...options });
}

/**
 * @description 获取当前登录用户有权限的页面
 * @param params 参数 {
 *   roletypes?： array
 *   roleid?： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1AuthorizationMyPage(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/authorization/my/page', { headers: getHeaders(), ...options, params });
}

/**
 * @description 获取当前登录用户所有有权限的专题id集合（专题实体或专题编目）
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1AuthorizationMyTopicIdSet(options: OptionsType = {}) {
  return get(apiName, '/v1/authorization/my/topic-id-set', { headers: getHeaders(), ...options });
}

/**
 * @description 获取当前登录用户指定专题编目下的信息和图层信息
 * @param catalogId string
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1AuthorizationMyTopicCatalogIdLayers(catalogId: string, options: OptionsType = {}) {
  return get(apiName, `/v1/authorization/my/topic/${catalogId}/layers`, { headers: getHeaders(), ...options });
}

/**
 * @description 获取当前登录用户有权限的组件
 * @param appletId string
 * @param params 参数 {
 *   roleid?： string
 *   roletypes?： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1AuthorizationMyAppletIdComponent(appletId: string, params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, `/v1/authorization/my/${appletId}/component`, { headers: getHeaders(), ...options, params });
}

/**
 * @description 获取当前登录用户有权限的专题
 * @param datasetIdOrCode string
 * @param params 参数 {
 *   pid?： string
 *   roletypes?： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1AuthorizationMyDatasetIdOrCodeTopic(datasetIdOrCode: string, params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, `/v1/authorization/my/${datasetIdOrCode}/topic`, { headers: getHeaders(), ...options, params });
}

/**
 * @description 获取指定用户有权限的页面
 * @param params 参数 {
 *   roletypes?： array
 *   roleid?： string
 *   userid： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1AuthorizationPage(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/authorization/page', { headers: getHeaders(), ...options, params });
}

/**
 * @description 根据资源id获取对资源可用权限的角色id信息
 * @param params 参数 {
 *   resourceids： array
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1AuthorizationRoleset(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/authorization/roleset', { headers: getHeaders(), ...options, params });
}

/**
 * @description 根据专题id获取被挂接的角色id信息
 * @param params 参数 {
 *   datasetcode： string
 *   topicid： string
 *   usable?： boolean
 *   visible?： boolean
 *   roleuname?： boolean
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1AuthorizationTopicRoleset(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/authorization/topic/roleset/', { headers: getHeaders(), ...options, params });
}

/**
 * @description 获取指定用户有权限的组件
 * @param appletId string
 * @param params 参数 {
 *   roleid?： string
 *   roletypes?： string
 *   userid： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1AuthorizationAppletIdComponent(appletId: string, params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, `/v1/authorization/${appletId}/component`, { headers: getHeaders(), ...options, params });
}

/**
 * @description 授予角色对组件的权限
 * @param roleId string
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1AuthorizationRoleIdComponent(roleId: string, body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, `/v1/authorization/${roleId}/component`, { body, headers: getHeaders(), ...options });
}

/**
 * @description 获取角色下的组件和操作权限
 * @param roleId string
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1AuthorizationRoleIdComponentPermission(roleId: string, options: OptionsType = {}) {
  return get(apiName, `/v1/authorization/${roleId}/component/permission`, { headers: getHeaders(), ...options });
}

/**
 * @description 授予角色对页面的权限
 * @param roleId string
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1AuthorizationRoleIdPage(roleId: string, body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, `/v1/authorization/${roleId}/page`, { body, headers: getHeaders(), ...options });
}

/**
 * @description 获取角色下的页面和操作权限
 * @param roleId string
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1AuthorizationRoleIdPagePermission(roleId: string, options: OptionsType = {}) {
  return get(apiName, `/v1/authorization/${roleId}/page/permission`, { headers: getHeaders(), ...options });
}

/**
 * @description 获取角色下的专题和操作权限
 * @param roleId string
 * @param datasetId string
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1AuthorizationRoleIdTopicDatasetIdPermission(roleId: string, datasetId: string, options: OptionsType = {}) {
  return get(apiName, `/v1/authorization/${roleId}/topic/${datasetId}/permission`, { headers: getHeaders(), ...options });
}

/**
 * @description 授予角色对专题的权限
 * @param roleId string
 * @param datasetId string
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1AuthorizationRoleIdDatasetIdTopic(roleId: string, datasetId: string, body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, `/v1/authorization/${roleId}/${datasetId}/topic`, { body, headers: getHeaders(), ...options });
}

/**
 * @description 获取数据标准
 * @param params 参数 {
 *   id： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1Datastandard(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/datastandard', { headers: getHeaders(), ...options, params });
}

/**
 * @description 新增或修改数据标准
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1Datastandard(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/datastandard', { body, headers: getHeaders(), ...options });
}

/**
 * @description 批量删除数据标准
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function deleteV1Datastandard(body: Record<string, any>, options: OptionsType = {}) {
  return del(apiName, '/v1/datastandard', { body, headers: getHeaders(), ...options });
}

/**
 * @description 导出数据标准
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1DatastandardExport(options: OptionsType = {}) {
  return get(apiName, '/v1/datastandard/export', { headers: getHeaders(), ...options });
}

/**
 * @description 获取数据标准字段
 * @param params 参数 {
 *   id： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1DatastandardField(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/datastandard/field', { headers: getHeaders(), ...options, params });
}

/**
 * @description 新增或修改数据标准字段
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1DatastandardField(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/datastandard/field', { body, headers: getHeaders(), ...options });
}

/**
 * @description 批量删除数据标准字段
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function deleteV1DatastandardField(body: Record<string, any>, options: OptionsType = {}) {
  return del(apiName, '/v1/datastandard/field', { body, headers: getHeaders(), ...options });
}

/**
 * @description 分页获取数据标准字段列表
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1DatastandardFieldPage(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/datastandard/field/page', { body, headers: getHeaders(), ...options });
}

/**
 * @description 获取数据标准字段排序信息
 * @param params 参数 {
 *   standardId： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1DatastandardFieldSort(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/datastandard/field/sort', { headers: getHeaders(), ...options, params });
}

/**
 * @description 排序数据标准字段
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1DatastandardFieldSort(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/datastandard/field/sort', { body, headers: getHeaders(), ...options });
}

/**
 * @description 导入数据标准
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1DatastandardImport(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/datastandard/import', { body, headers: getHeaders(), ...options });
}

/**
 * @description 分页获取数据标准列表
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1DatastandardPage(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/datastandard/page', { body, headers: getHeaders(), ...options });
}

/**
 * @description 获取数据集
 * @param params 参数 {
 *   pageindex： integer
 *   pagesize： integer
 *   status?： integer
 *   keyword?： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1Datasets(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/datasets', { headers: getHeaders(), ...options, params });
}

/**
 * @description 更新数据集
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function putV1Datasets(body: Record<string, any>, options: OptionsType = {}) {
  return put(apiName, '/v1/datasets', { body, headers: getHeaders(), ...options });
}

/**
 * @description 添加数据集
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1Datasets(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/datasets', { body, headers: getHeaders(), ...options });
}

/**
 * @description 逻辑删除数据集（传入数据集id数组）
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function deleteV1Datasets(body: Record<string, any>, options: OptionsType = {}) {
  return del(apiName, '/v1/datasets', { body, headers: getHeaders(), ...options });
}

/**
 * @description 批量获取专题图层信息（返回Map，key为专题id，value是图层信息）
 * @param params 参数 {
 *   latest?： boolean
 * }
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1DatasetsBatchTopicLayers(params: Record<string, any>, body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/datasets/batch-topic/layers', { body, headers: getHeaders(), ...options, params });
}

/**
 * @description 根据专题目录批量获取专题图层信息（返回Map，key为专题id，value是图层信息）
 * @param params 参数 {
 *   latest?： boolean
 * }
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1DatasetsBatchTopicLayersByCatalog(params: Record<string, any>, body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/datasets/batch-topic/layers/by-catalog', { body, headers: getHeaders(), ...options, params });
}

/**
 * @description 根据数据集批量获取专题图层信息（返回Map，key为专题id，value是图层信息）
 * @param params 参数 {
 *   latest?： boolean
 * }
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1DatasetsBatchTopicLayersByDataset(params: Record<string, any>, body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/datasets/batch-topic/layers/by-dataset', { body, headers: getHeaders(), ...options, params });
}

/**
 * @description 清理数据集脏数据（清理被标识为删除的数据）
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function deleteV1DatasetsClearDirty(options: OptionsType = {}) {
  return del(apiName, '/v1/datasets/clear/dirty', { headers: getHeaders(), ...options });
}

/**
 * @description 复制目录或专题到指定的目录下
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1DatasetsCopyTo(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/datasets/copy/to', { body, headers: getHeaders(), ...options });
}

/**
 * @description 更新目录元数据信息
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function putV1DatasetsDir(body: Record<string, any>, options: OptionsType = {}) {
  return put(apiName, '/v1/datasets/dir', { body, headers: getHeaders(), ...options });
}

/**
 * @description 导出数据集（方案）
 * @param datasetId string
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1DatasetsExportDatasetId(datasetId: string, options: OptionsType = {}) {
  return get(apiName, `/v1/datasets/export/${datasetId}`, { headers: getHeaders(), ...options });
}

/**
 * @description 下载文件
 * @param params 参数 {
 *   fileId： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1DatasetsFileDownload(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/datasets/file/download', { headers: getHeaders(), ...options, params });
}

/**
 * @description 导入数据集（方案）
 * @param params 参数 {
 *   mode?： string
 *   newName?： string
 *   newCode?： string
 * }
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1DatasetsImport(params: Record<string, any>, body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/datasets/import', { body, headers: getHeaders(), ...options, params });
}

/**
 * @description 移动目录或专题到指定的目录下
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1DatasetsMoveTo(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/datasets/move/to', { body, headers: getHeaders(), ...options });
}

/**
 * @description 上下移动编目/专题（只能同级别）
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1DatasetsMoveUpdown(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/datasets/move/updown', { body, headers: getHeaders(), ...options });
}

/**
 * @description 刷新数据集地址信息（专题关联的地图服务协议、ip和端口。）
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function putV1DatasetsRefreshAddress(body: Record<string, any>, options: OptionsType = {}) {
  return put(apiName, '/v1/datasets/refresh-address', { body, headers: getHeaders(), ...options });
}

/**
 * @description 物理删除数据集
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function deleteV1DatasetsRemove(body: Record<string, any>, options: OptionsType = {}) {
  return del(apiName, '/v1/datasets/remove', { body, headers: getHeaders(), ...options });
}

/**
 * @description 修复专题服务id值（临时性处理）
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function putV1DatasetsRepairTopicServiceid(body: Record<string, any>, options: OptionsType = {}) {
  return put(apiName, '/v1/datasets/repair/topic/serviceid', { body, headers: getHeaders(), ...options });
}

/**
 * @description 同步更新专题服务连接地址信息（专题关联的地图服务协议、ip和端口。）
 * @param params 参数 {
 *   site-id： string
 *   service-id： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function putV1DatasetsSyncTopicAddress(params: Record<string, any>, options: OptionsType = {}) {
  return put(apiName, '/v1/datasets/sync-topic-address', { headers: getHeaders(), ...options, params });
}

/**
 * @description 同步更新数据集下专题信息
 * @param params 参数 {
 *   id： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1DatasetsSyncTopicsByDataset(params: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/datasets/sync-topics/by-dataset', { headers: getHeaders(), ...options, params });
}

/**
 * @description 更新专题元数据信息
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function putV1DatasetsTopic(body: Record<string, any>, options: OptionsType = {}) {
  return put(apiName, '/v1/datasets/topic', { body, headers: getHeaders(), ...options });
}

/**
 * @description 删除编目/专题
 * @param catalogId string
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function deleteV1DatasetsCatalogId(catalogId: string, options: OptionsType = {}) {
  return del(apiName, `/v1/datasets/${catalogId}`, { headers: getHeaders(), ...options });
}

/**
 * @description 构建数据集的配置内容
 * @param datasetIdOrUniqueCode string
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1DatasetsDatasetIdOrUniqueCodeContentBuild(datasetIdOrUniqueCode: string, options: OptionsType = {}) {
  return post(apiName, `/v1/datasets/${datasetIdOrUniqueCode}/content/build`, { headers: getHeaders(), ...options });
}

/**
 * @description 获取数据集的配置内容
 * @param datasetId string
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1DatasetsDatasetIdContent(datasetId: string, options: OptionsType = {}) {
  return get(apiName, `/v1/datasets/${datasetId}/content`, { headers: getHeaders(), ...options });
}

/**
 * @description 添加目录
 * @param datasetId string
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1DatasetsDatasetIdDir(datasetId: string, body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, `/v1/datasets/${datasetId}/dir`, { body, headers: getHeaders(), ...options });
}

/**
 * @description 模糊检索数据集下的目录和专题
 * @param datasetId string
 * @param params 参数 {
 *   pageindex： integer
 *   pagesize： integer
 *   keyword?： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1DatasetsDatasetIdSearch(datasetId: string, params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, `/v1/datasets/${datasetId}/search`, { headers: getHeaders(), ...options, params });
}

/**
 * @description 获取编目子级节点信息
 * @param datasetId string
 * @param params 参数 {
 *   pid?： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1DatasetsDatasetIdSubcatalog(datasetId: string, params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, `/v1/datasets/${datasetId}/subcatalog`, { headers: getHeaders(), ...options, params });
}

/**
 * @description 添加专题
 * @param datasetId string
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1DatasetsDatasetIdTopic(datasetId: string, body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, `/v1/datasets/${datasetId}/topic`, { body, headers: getHeaders(), ...options });
}

/**
 * @description 删除专题封面文件
 * @param topicId string
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function deleteV1DatasetsTopicIdCoverClear(topicId: string, options: OptionsType = {}) {
  return del(apiName, `/v1/datasets/${topicId}/cover/clear`, { headers: getHeaders(), ...options });
}

/**
 * @description 获取专题图层信息
 * @param topicId string
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1DatasetsTopicIdLayers(topicId: string, options: OptionsType = {}) {
  return get(apiName, `/v1/datasets/${topicId}/layers`, { headers: getHeaders(), ...options });
}

/**
 * @description 更新专题图层信息（替换图层信息）
 * @param topicId string
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function putV1DatasetsTopicIdLayers(topicId: string, body: Record<string, any>, options: OptionsType = {}) {
  return put(apiName, `/v1/datasets/${topicId}/layers`, { body, headers: getHeaders(), ...options });
}

/**
 * @description 添加专题图层信息（追加图层信息）
 * @param topicId string
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1DatasetsTopicIdLayers(topicId: string, body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, `/v1/datasets/${topicId}/layers`, { body, headers: getHeaders(), ...options });
}

/**
 * @description 清空专题图层信息
 * @param topicId string
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1DatasetsTopicIdLayersClear(topicId: string, options: OptionsType = {}) {
  return get(apiName, `/v1/datasets/${topicId}/layers/clear`, { headers: getHeaders(), ...options });
}

/**
 * @description 更新专题元数据信息（以form表单请求方式，扩展专题缩略图需求）
 * @param params 参数 {
 *   defaultOpen?： boolean
 *   ext?： string
 *   file?： string
 *   height?： integer
 *   id： string
 *   name： string
 *   opacity?： number
 *   remark?： string
 *   width?： integer
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function putV2DatasetsTopic(params: Record<string, any>, options: OptionsType = {}) {
  return put(apiName, '/v2/datasets/topic', { headers: getHeaders(), ...options, params });
}

/**
 * @description 添加专题（以form表单请求方式，扩展专题缩略图需求）
 * @param datasetId string
 * @param params 参数 {
 *   defaultOpen?： boolean
 *   ext?： string
 *   file?： string
 *   height?： integer
 *   name： string
 *   opacity?： number
 *   parentId?： string
 *   remark?： string
 *   width?： integer
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV2DatasetsDatasetIdTopic(datasetId: string, params: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, `/v2/datasets/${datasetId}/topic`, { headers: getHeaders(), ...options, params });
}

/**
 * @description 修改数据源
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function putV1Datasource(body: Record<string, any>, options: OptionsType = {}) {
  return put(apiName, '/v1/datasource', { body, headers: getHeaders(), ...options });
}

/**
 * @description 新增数据源
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1Datasource(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/datasource', { body, headers: getHeaders(), ...options });
}

/**
 * @description 删除数据源
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function deleteV1Datasource(body: Record<string, any>, options: OptionsType = {}) {
  return del(apiName, '/v1/datasource', { body, headers: getHeaders(), ...options });
}

/**
 * @description 分页获取数据源
 * @param index string
 * @param size string
 * @param params 参数 {
 *   name?： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1DatasourcePageIndexSize(index: string, size: string, params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, `/v1/datasource/page/${index}/${size}`, { headers: getHeaders(), ...options, params });
}

/**
 * @description 获取数据源模式
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1DatasourceSchema(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/datasource/schema', { body, headers: getHeaders(), ...options });
}

/**
 * @description 获取数据源类型
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1DatasourceType(options: OptionsType = {}) {
  return get(apiName, '/v1/datasource/type', { headers: getHeaders(), ...options });
}

/**
 * @description 验证数据源连接
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1DatasourceValid(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/datasource/valid', { body, headers: getHeaders(), ...options });
}

/**
 * @description 获取数据源详情
 * @param id string
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1DatasourceId(id: string, options: OptionsType = {}) {
  return get(apiName, `/v1/datasource/${id}`, { headers: getHeaders(), ...options });
}

/**
 * @description 获取sde数据源下的图层信息
 * @param id string
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1DatasourceIdSdeTable(id: string, options: OptionsType = {}) {
  return get(apiName, `/v1/datasource/${id}/sde/table`, { headers: getHeaders(), ...options });
}

/**
 * @description 获取sde数据源下的图层字段别名
 * @param id string
 * @param table string
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1DatasourceIdSdeTableColumn(id: string, table: string, options: OptionsType = {}) {
  return get(apiName, `/v1/datasource/${id}/sde/${table}/column`, { headers: getHeaders(), ...options });
}

/**
 * @description 获取数据源下的表
 * @param id string
 * @param params 参数 {
 *   ignoreSDE?： boolean
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1DatasourceIdTable(id: string, params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, `/v1/datasource/${id}/table`, { headers: getHeaders(), ...options, params });
}

/**
 * @description 获取数据源下的表元数据信息
 * @param id string
 * @param params 参数 {
 *   tableName： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1DatasourceIdTableMeta(id: string, params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, `/v1/datasource/${id}/table/meta`, { headers: getHeaders(), ...options, params });
}

/**
 * @description 获取字典列表
 * @param params 参数 {
 *   pageindex： integer
 *   pagesize： integer
 *   keyword?： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1Dicts(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/dicts', { headers: getHeaders(), ...options, params });
}

/**
 * @description 修改字典
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function putV1Dicts(body: Record<string, any>, options: OptionsType = {}) {
  return put(apiName, '/v1/dicts', { body, headers: getHeaders(), ...options });
}

/**
 * @description 添加字典
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1Dicts(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/dicts', { body, headers: getHeaders(), ...options });
}

/**
 * @description 批量删除字典（传入字典id数组）
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function deleteV1Dicts(body: Record<string, any>, options: OptionsType = {}) {
  return del(apiName, '/v1/dicts', { body, headers: getHeaders(), ...options });
}

/**
 * @description 根据字典标识获取字典
 * @param dictId string
 * @param params 参数 {
 *   detail?： boolean
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1DictsByidDictId(dictId: string, params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, `/v1/dicts/byid/${dictId}`, { headers: getHeaders(), ...options, params });
}

/**
 * @description 根据字典唯一编码获取字典下字典项
 * @param dictCode string
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1DictsCodeDictCodeItems(dictCode: string, options: OptionsType = {}) {
  return get(apiName, `/v1/dicts/code/${dictCode}/items`, { headers: getHeaders(), ...options });
}

/**
 * @description 导出字典
 * @param params 参数 {
 *   dicIds?： array
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1DictsExport(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/dicts/export', { headers: getHeaders(), ...options, params });
}

/**
 * @description 导入字典
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1DictsImport(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/dicts/import', { body, headers: getHeaders(), ...options });
}

/**
 * @description 修改指定字典下的字典项信息
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function putV1DictsItem(body: Record<string, any>, options: OptionsType = {}) {
  return put(apiName, '/v1/dicts/item', { body, headers: getHeaders(), ...options });
}

/**
 * @description 修复字典项父级id
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1DictsItemRepair(options: OptionsType = {}) {
  return post(apiName, '/v1/dicts/item/repair', { headers: getHeaders(), ...options });
}

/**
 * @description 字典项上下移动
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1DictsItemUpdown(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/dicts/item/updown', { body, headers: getHeaders(), ...options });
}

/**
 * @description 全部覆盖字典项
 * @param dictId string
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1DictsReplaceDictIdItems(dictId: string, body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, `/v1/dicts/replace/${dictId}/items`, { body, headers: getHeaders(), ...options });
}

/**
 * @description 获取字典类型列表
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1DictsType(options: OptionsType = {}) {
  return get(apiName, '/v1/dicts/type', { headers: getHeaders(), ...options });
}

/**
 * @description 修改字典类型
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function putV1DictsType(body: Record<string, any>, options: OptionsType = {}) {
  return put(apiName, '/v1/dicts/type', { body, headers: getHeaders(), ...options });
}

/**
 * @description 添加字典类型
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1DictsType(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/dicts/type', { body, headers: getHeaders(), ...options });
}

/**
 * @description 删除字典类型（传入字典类型唯一id集合）
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function deleteV1DictsType(body: Record<string, any>, options: OptionsType = {}) {
  return del(apiName, '/v1/dicts/type', { body, headers: getHeaders(), ...options });
}

/**
 * @description 字典上下移动
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1DictsUpdown(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/dicts/updown', { body, headers: getHeaders(), ...options });
}

/**
 * @description 根据字典编码获取字典
 * @param dictCode string
 * @param params 参数 {
 *   detail?： boolean
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1DictsDictCode(dictCode: string, params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, `/v1/dicts/${dictCode}`, { headers: getHeaders(), ...options, params });
}

/**
 * @description 获取字典下指定字典值的字典项
 * @param dictId string
 * @param params 参数 {
 *   iv： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1DictsDictIdItem(dictId: string, params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, `/v1/dicts/${dictId}/item`, { headers: getHeaders(), ...options, params });
}

/**
 * @description 根据字典唯一标识获取字典下字典项
 * @param dictId string
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1DictsDictIdItems(dictId: string, options: OptionsType = {}) {
  return get(apiName, `/v1/dicts/${dictId}/items`, { headers: getHeaders(), ...options });
}

/**
 * @description 添加字典项（补充在原来字典项后面）
 * @param dictId string
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1DictsDictIdItems(dictId: string, body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, `/v1/dicts/${dictId}/items`, { body, headers: getHeaders(), ...options });
}

/**
 * @description 批量删除字典项（传入字典项id数组）
 * @param dictId string
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function deleteV1DictsDictIdItems(dictId: string, body: Record<string, any>, options: OptionsType = {}) {
  return del(apiName, `/v1/dicts/${dictId}/items`, { body, headers: getHeaders(), ...options });
}

/**
 * @description 根据字典唯一编码获取字典下字典项V2
 * @param dictCode string
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV2DictsCodeDictCodeItems(dictCode: string, options: OptionsType = {}) {
  return get(apiName, `/v2/dicts/code/${dictCode}/items`, { headers: getHeaders(), ...options });
}

/**
 * @description 根据字典目录和字典唯一编码获取字典
 * @param dictCode string
 * @param dicCode string
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV2DictsCodeDictCodeDicCodeItems(dictCode: string, dicCode: string, options: OptionsType = {}) {
  return get(apiName, `/v2/dicts/code/${dictCode}/${dicCode}/items`, { headers: getHeaders(), ...options });
}

/**
 * @description 修改指定字典下的字典项信息V2
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function putV2DictsItem(body: Record<string, any>, options: OptionsType = {}) {
  return put(apiName, '/v2/dicts/item', { body, headers: getHeaders(), ...options });
}

/**
 * @description 新增字典项
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV2DictsItem(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v2/dicts/item', { body, headers: getHeaders(), ...options });
}

/**
 * @description 批量删除字典项（传入字典项Id数组）
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function deleteV2DictsItems(body: Record<string, any>, options: OptionsType = {}) {
  return del(apiName, '/v2/dicts/items', { body, headers: getHeaders(), ...options });
}

/**
 * @description 根据一级字典项id获取字典【树形】
 * @param dicItemId string
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV2DictsItemsDicItemId(dicItemId: string, options: OptionsType = {}) {
  return get(apiName, `/v2/dicts/items/${dicItemId}`, { headers: getHeaders(), ...options });
}

/**
 * @description 获取字典项目录树
 * @param dictId string
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV2DictsDictIdItems(dictId: string, options: OptionsType = {}) {
  return get(apiName, `/v2/dicts/${dictId}/items`, { headers: getHeaders(), ...options });
}

/**
 * @description 导出日志
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1LogsExp(options: OptionsType = {}) {
  return get(apiName, '/v1/logs/exp', { headers: getHeaders(), ...options });
}

/**
 * @description 添加或修改元数据信息
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1Metadata(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/metadata', { body, headers: getHeaders(), ...options });
}

/**
 * @description 删除元数据信息
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function deleteV1Metadata(body: Record<string, any>, options: OptionsType = {}) {
  return del(apiName, '/v1/metadata', { body, headers: getHeaders(), ...options });
}

/**
 * @description 获取元数据的数据类型枚举列表
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1MetadataDatatype(options: OptionsType = {}) {
  return get(apiName, '/v1/metadata/datatype', { headers: getHeaders(), ...options });
}

/**
 * @description 获取元数据要素信息
 * @param params 参数 {
 *   metaDataElementId： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1MetadataElement(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/metadata/element', { headers: getHeaders(), ...options, params });
}

/**
 * @description 添加或修改元数据要素信息
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1MetadataElement(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/metadata/element', { body, headers: getHeaders(), ...options });
}

/**
 * @description 删除元数据要素信息
 * @param params 参数 {
 *   metaDataElementId： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function deleteV1MetadataElement(params: Record<string, any>, options: OptionsType = {}) {
  return del(apiName, '/v1/metadata/element', { headers: getHeaders(), ...options, params });
}

/**
 * @description 获取模型关联的全部元信息
 * @param params 参数 {
 *   metaDataModelId： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1MetadataList(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/metadata/list', { headers: getHeaders(), ...options, params });
}

/**
 * @description 获取元数据目录类型信息
 * @param params 参数 {
 *   metaDataModelId： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1MetadataModel(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/metadata/model', { headers: getHeaders(), ...options, params });
}

/**
 * @description 添加或修改元数据模型信息
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1MetadataModel(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/metadata/model', { body, headers: getHeaders(), ...options });
}

/**
 * @description 删除元数据模型信息
 * @param params 参数 {
 *   metaDataModelId： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function deleteV1MetadataModel(params: Record<string, any>, options: OptionsType = {}) {
  return del(apiName, '/v1/metadata/model', { headers: getHeaders(), ...options, params });
}

/**
 * @description 对模板所绑定的元数据进行手动排序
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function putV1MetadataModelDataSort(body: Record<string, any>, options: OptionsType = {}) {
  return put(apiName, '/v1/metadata/model/data/sort', { body, headers: getHeaders(), ...options });
}

/**
 * @description 编辑模板关联元数据信息
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function putV1MetadataModelLink(body: Record<string, any>, options: OptionsType = {}) {
  return put(apiName, '/v1/metadata/model/link', { body, headers: getHeaders(), ...options });
}

/**
 * @description 模板关联元数据
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1MetadataModelLink(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/metadata/model/link', { body, headers: getHeaders(), ...options });
}

/**
 * @description 模板取消关联元数据
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function deleteV1MetadataModelLink(body: Record<string, any>, options: OptionsType = {}) {
  return del(apiName, '/v1/metadata/model/link', { body, headers: getHeaders(), ...options });
}

/**
 * @description 分页获取元数据信息
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1MetadataPage(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/metadata/page', { body, headers: getHeaders(), ...options });
}

/**
 * @description 添加或修改元数据体系
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1MetadataSystem(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/metadata/system', { body, headers: getHeaders(), ...options });
}

/**
 * @description 导出元数据体系目录及要素
 * @param params 参数 {
 *   systemId?： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1MetadataSystemExport(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/metadata/system/export', { headers: getHeaders(), ...options, params });
}

/**
 * @description 导入元数据体系目录及要素【对于已存在的体系会校验体系下是否有数据，如果有建议手动删除后再导入】
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1MetadataSystemImport(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/metadata/system/import', { body, headers: getHeaders(), ...options });
}

/**
 * @description 获取体系目录下的标准列表
 * @param systemDirId string
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1MetadataSystemSystemDirIdStandards(systemDirId: string, options: OptionsType = {}) {
  return get(apiName, `/v1/metadata/system/${systemDirId}/standards`, { headers: getHeaders(), ...options });
}

/**
 * @description 获取元数据目录类型下面树形元数据
 * @param params 参数 {
 *   type： string
 *   allNodes： boolean
 *   needMetaData： boolean
 *   metaDataTypeId： string
 *   dirKeyword?： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1MetadataTree(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/metadata/tree', { headers: getHeaders(), ...options, params });
}

/**
 * @description 获取元数据目录类型信息
 * @param params 参数 {
 *   metaDataTypeId： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1MetadataType(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/metadata/type', { headers: getHeaders(), ...options, params });
}

/**
 * @description 添加或修改元数据目录类型信息
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1MetadataType(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/metadata/type', { body, headers: getHeaders(), ...options });
}

/**
 * @description 删除元数据目录类型信息
 * @param params 参数 {
 *   metaDataTypeId： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function deleteV1MetadataType(params: Record<string, any>, options: OptionsType = {}) {
  return del(apiName, '/v1/metadata/type', { headers: getHeaders(), ...options, params });
}

/**
 * @description 交换两个目录的位置
 * @param params 参数 {
 *   typeId1： string
 *   typeId2： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function putV1MetadataTypeSwap(params: Record<string, any>, options: OptionsType = {}) {
  return put(apiName, '/v1/metadata/type/swap', { headers: getHeaders(), ...options, params });
}

/**
 * @description 修改元数据值
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function putV1MetadataValue(body: Record<string, any>, options: OptionsType = {}) {
  return put(apiName, '/v1/metadata/value', { body, headers: getHeaders(), ...options });
}

/**
 * @description 删除元数据值
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function deleteV1MetadataValue(body: Record<string, any>, options: OptionsType = {}) {
  return del(apiName, '/v1/metadata/value', { body, headers: getHeaders(), ...options });
}

/**
 * @description 获取元数据值异常状态【true表示生成了异常文件,false表示无异常】
 * @param params 参数 {
 *   modelId： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1MetadataValueCheck(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/metadata/value/check', { headers: getHeaders(), ...options, params });
}

/**
 * @description 导出元数据值
 * @param params 参数 {
 *   typeId： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1MetadataValueExport(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/metadata/value/export', { headers: getHeaders(), ...options, params });
}

/**
 * @description 获取元数据值异常文件
 * @param params 参数 {
 *   modelId： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1MetadataValueFile(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/metadata/value/file', { headers: getHeaders(), ...options, params });
}

/**
 * @description 删除元数据值异常文件
 * @param params 参数 {
 *   modelId： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function deleteV1MetadataValueFile(params: Record<string, any>, options: OptionsType = {}) {
  return del(apiName, '/v1/metadata/value/file', { headers: getHeaders(), ...options, params });
}

/**
 * @description 获取元数据值列表
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1MetadataValueList(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/metadata/value/list', { body, headers: getHeaders(), ...options });
}

/**
 * @description 更新元数据值的图斑数
 * @param params 参数 {
 *   typeId： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function putV1MetadataValueShapeCount(params: Record<string, any>, options: OptionsType = {}) {
  return put(apiName, '/v1/metadata/value/shape/count', { headers: getHeaders(), ...options, params });
}

/**
 * @description 上传元数据值并校验
 * @param params 参数 {
 *   modelId： string
 * }
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1MetadataValueUpload(params: Record<string, any>, body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/metadata/value/upload', { body, headers: getHeaders(), ...options, params });
}

/**
 * @description 获取组件节点信息
 * @param params 参数 {
 *   pid?： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1UiResourcesComponent(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/ui-resources/component', { headers: getHeaders(), ...options, params });
}

/**
 * @description 添加或更新组件
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1UiResourcesComponent(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/ui-resources/component', { body, headers: getHeaders(), ...options });
}

/**
 * @description 复制组件
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1UiResourcesComponentCopy(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/ui-resources/component/copy', { body, headers: getHeaders(), ...options });
}

/**
 * @description 获取组件树状数据
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1UiResourcesComponentTree(options: OptionsType = {}) {
  return get(apiName, '/v1/ui-resources/component/tree', { headers: getHeaders(), ...options });
}

/**
 * @description 添加或更新组件下的操作
 * @param componentId string
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1UiResourcesComponentComponentIdOperation(componentId: string, body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, `/v1/ui-resources/component/${componentId}/operation`, { body, headers: getHeaders(), ...options });
}

/**
 * @description 获取资源详情信息
 * @param resourceId string
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1UiResourcesDetailResourceId(resourceId: string, options: OptionsType = {}) {
  return get(apiName, `/v1/ui-resources/detail/${resourceId}`, { headers: getHeaders(), ...options });
}

/**
 * @description 导出菜单/组件（ids传空导出全部）
 * @param params 参数 {
 *   type： integer
 * }
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1UiResourcesExport(params: Record<string, any>, body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/ui-resources/export', { body, headers: getHeaders(), ...options, params });
}

/**
 * @description 导入菜单/组件
 * @param params 参数 {
 *   parentId?： string
 * }
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1UiResourcesImport(params: Record<string, any>, body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/ui-resources/import', { body, headers: getHeaders(), ...options, params });
}

/**
 * @description 移动资源到指定的资源下
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1UiResourcesMoveTo(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/ui-resources/move/to', { body, headers: getHeaders(), ...options });
}

/**
 * @description 上下移动资源（只能同级别）
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1UiResourcesMoveUpdown(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/ui-resources/move/updown', { body, headers: getHeaders(), ...options });
}

/**
 * @description 获取页面节点信息
 * @param params 参数 {
 *   pid?： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1UiResourcesPage(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/ui-resources/page', { headers: getHeaders(), ...options, params });
}

/**
 * @description 添加或更新页面
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1UiResourcesPage(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/ui-resources/page', { body, headers: getHeaders(), ...options });
}

/**
 * @description 获取页面树状数据
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1UiResourcesPageTree(options: OptionsType = {}) {
  return get(apiName, '/v1/ui-resources/page/tree', { headers: getHeaders(), ...options });
}

/**
 * @description 添加或更新页面下的操作
 * @param pageId string
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1UiResourcesPagePageIdOperation(pageId: string, body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, `/v1/ui-resources/page/${pageId}/operation`, { body, headers: getHeaders(), ...options });
}

/**
 * @description 组件检索
 * @param params 参数 {
 *   keyword： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1UiResourcesSearchComponent(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/ui-resources/search/component', { headers: getHeaders(), ...options, params });
}

/**
 * @description 页面检索
 * @param params 参数 {
 *   keyword： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1UiResourcesSearchPage(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/ui-resources/search/page', { headers: getHeaders(), ...options, params });
}

/**
 * @description 删除资源（级联删除相关的子节点）
 * @param resourceId string
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function deleteV1UiResourcesResourceId(resourceId: string, options: OptionsType = {}) {
  return del(apiName, `/v1/ui-resources/${resourceId}`, { headers: getHeaders(), ...options });
}

/**
 * @description 分页获取我创建的场景
 * @param params 参数 {
 *   pageindex： integer
 *   pagesize： integer
 *   keyword?： string
 *   appletid： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1UcScenes(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/uc/scenes', { headers: getHeaders(), ...options, params });
}

/**
 * @description 保存我的场景
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1UcScenes(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/uc/scenes', { body, headers: getHeaders(), ...options });
}

/**
 * @description 删除我创建的场景信息
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function deleteV1UcScenes(body: Record<string, any>, options: OptionsType = {}) {
  return del(apiName, '/v1/uc/scenes', { body, headers: getHeaders(), ...options });
}

/**
 * @description 分页获取我收藏的专题
 * @param params 参数 {
 *   pageindex： integer
 *   pagesize： integer
 *   appletid： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1UcTopics(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/uc/topics', { headers: getHeaders(), ...options, params });
}

/**
 * @description 保存个人专题收藏
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1UcTopics(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/uc/topics', { body, headers: getHeaders(), ...options });
}

/**
 * @description 取消个人专题收藏
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function deleteV1UcTopics(body: Record<string, any>, options: OptionsType = {}) {
  return del(apiName, '/v1/uc/topics', { body, headers: getHeaders(), ...options });
}

/**
 * @description 保存个人常用界面工具资源
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1UcUiResources(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/uc/ui-resources', { body, headers: getHeaders(), ...options });
}

/**
 * @description 获取个人常用界面工具资源
 * @param appletId string
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1UcUiResourcesAppletId(appletId: string, options: OptionsType = {}) {
  return get(apiName, `/v1/uc/ui-resources/${appletId}`, { headers: getHeaders(), ...options });
}

/**
 * @description 删除个人常用界面工具资源
 * @param appletId string
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function deleteV1UcUiResourcesAppletId(appletId: string, options: OptionsType = {}) {
  return del(apiName, `/v1/uc/ui-resources/${appletId}`, { headers: getHeaders(), ...options });
}

/**
 * @description 模糊检索行政区划（已过时，切换到接口：/v2/zonings/searchbyscope，或/v2/zonings/searchbyversion）
 * @param params 参数 {
 *   pageindex： integer
 *   pagesize： integer
 *   pzc?： string
 *   keyword： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1Zonings(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/zonings', { headers: getHeaders(), ...options, params });
}

/**
 * @description 更新行政区划
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function putV1Zonings(body: Record<string, any>, options: OptionsType = {}) {
  return put(apiName, '/v1/zonings', { body, headers: getHeaders(), ...options });
}

/**
 * @description 添加行政区划
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1Zonings(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/zonings', { body, headers: getHeaders(), ...options });
}

/**
 * @description 删除行政区划（传入区划id数组）
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function deleteV1Zonings(body: Record<string, any>, options: OptionsType = {}) {
  return del(apiName, '/v1/zonings', { body, headers: getHeaders(), ...options });
}

/**
 * @description 设置默认的行政区版本
 * @param versionId string
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function putV1ZoningsDefaultVersionId(versionId: string, options: OptionsType = {}) {
  return put(apiName, `/v1/zonings/default/${versionId}`, { headers: getHeaders(), ...options });
}

/**
 * @description 区划级别
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1ZoningsLevel(options: OptionsType = {}) {
  return get(apiName, '/v1/zonings/level', { headers: getHeaders(), ...options });
}

/**
 * @description 获取行政区划列表
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1ZoningsList(options: OptionsType = {}) {
  return get(apiName, '/v1/zonings/list', { headers: getHeaders(), ...options });
}

/**
 * @description 获取当前登录者的行政区划树形数据
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1ZoningsMyTree(options: OptionsType = {}) {
  return get(apiName, '/v1/zonings/my/tree', { headers: getHeaders(), ...options });
}

/**
 * @description 根据行政区编码，获取行政区划子级树形数据
 * @param parentZoningCode string
 * @param params 参数 {
 *   allchildren?： boolean
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1ZoningsSubtreeParentZoningCode(parentZoningCode: string, params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, `/v1/zonings/subtree/${parentZoningCode}`, { headers: getHeaders(), ...options, params });
}

/**
 * @description 获取行政区划树形数据
 * @param params 参数 {
 *   parentid?： string
 *   allchildren?： boolean
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1ZoningsTree(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v1/zonings/tree', { headers: getHeaders(), ...options, params });
}

/**
 * @description 行政区上下移动（同级）
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1ZoningsUpdown(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/zonings/updown', { body, headers: getHeaders(), ...options });
}

/**
 * @description 更新版本
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function putV1ZoningsVersion(body: Record<string, any>, options: OptionsType = {}) {
  return put(apiName, '/v1/zonings/version', { body, headers: getHeaders(), ...options });
}

/**
 * @description 添加新版本
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1ZoningsVersion(body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, '/v1/zonings/version', { body, headers: getHeaders(), ...options });
}

/**
 * @description 拷贝行政区划版本以及行政区数据
 * @param versionId string
 * @param params 参数 {
 *   versionId： string
 *   name?： string
 *   scope： string
 *   containdata： boolean
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV1ZoningsVersionCopyVersionId(versionId: string, params: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, `/v1/zonings/version/copy/${versionId}`, { headers: getHeaders(), ...options, params });
}

/**
 * @description 获取行政区版本列表
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV1ZoningsVersions(options: OptionsType = {}) {
  return get(apiName, '/v1/zonings/versions', { headers: getHeaders(), ...options });
}

/**
 * @description 获取行政区划列表
 * @param versionId string
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV2ZoningsListVersionId(versionId: string, options: OptionsType = {}) {
  return get(apiName, `/v2/zonings/list/${versionId}`, { headers: getHeaders(), ...options });
}

/**
 * @description 根据适用范围过滤模糊检索行政区划
 * @param params 参数 {
 *   scope： string
 *   pageindex： integer
 *   pagesize： integer
 *   pzc?： string
 *   keyword： string
 *   levels?： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV2ZoningsSearchbyscope(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v2/zonings/searchbyscope', { headers: getHeaders(), ...options, params });
}

/**
 * @description 根据版本id过滤模糊检索行政区划
 * @param params 参数 {
 *   vid： string
 *   pageindex： integer
 *   pagesize： integer
 *   pzc?： string
 *   keyword： string
 *   levels?： string
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV2ZoningsSearchbyversion(params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, '/v2/zonings/searchbyversion', { headers: getHeaders(), ...options, params });
}

/**
 * @description 根据行政区编码和适用范围唯一值，获取行政区划子级树形数据
 * @param parentZoningCode string
 * @param params 参数 {
 *   scope： string
 *   allchildren?： boolean
 *   standard?： boolean
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV2ZoningsSubtreeParentZoningCode(parentZoningCode: string, params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, `/v2/zonings/subtree/${parentZoningCode}`, { headers: getHeaders(), ...options, params });
}

/**
 * @description 根据行政区编码，获取行政区划子级树形数据
 * @param versionId string
 * @param parentZoningCode string
 * @param params 参数 {
 *   allchildren?： boolean
 *   standard?： boolean
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV2ZoningsSubtreeVersionIdParentZoningCode(versionId: string, parentZoningCode: string, params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, `/v2/zonings/subtree/${versionId}/${parentZoningCode}`, { headers: getHeaders(), ...options, params });
}

/**
 * @description 获取行政区划树形数据
 * @param versionId string
 * @param params 参数 {
 *   parentid?： string
 *   allchildren?： boolean
 *   standard?： boolean
 * }
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function getV2ZoningsTreeVersionId(versionId: string, params: Record<string, any>, options: OptionsType = {}) {
  return get(apiName, `/v2/zonings/tree/${versionId}`, { headers: getHeaders(), ...options, params });
}

/**
 * @description 更新行政区划
 * @param versionId string
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function putV2ZoningsVersionId(versionId: string, body: Record<string, any>, options: OptionsType = {}) {
  return put(apiName, `/v2/zonings/${versionId}`, { body, headers: getHeaders(), ...options });
}

/**
 * @description 添加行政区划
 * @param versionId string
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function postV2ZoningsVersionId(versionId: string, body: Record<string, any>, options: OptionsType = {}) {
  return post(apiName, `/v2/zonings/${versionId}`, { body, headers: getHeaders(), ...options });
}

/**
 * @description 删除行政区划（传入区划id数组）
 * @param versionId string
 * @param body Record<string, any> 请求体
 * @param options OptionsType 请求配置
 * @returns Promise<any>
 */
function deleteV2ZoningsVersionId(versionId: string, body: Record<string, any>, options: OptionsType = {}) {
  return del(apiName, `/v2/zonings/${versionId}`, { body, headers: getHeaders(), ...options });
}

export const syskeeperApi = {
  getV1Applets,
  postV1Applets,
  deleteV1Applets,
  postV1AppletsCopyAppletId,
  getV1AppletsIconBase64AppletId,
  getV1AppletsIconAppletId,
  getV1AppletsAppletId,
  putV1AppletsAppletId,
  getV1AppletsAppletIdComponent,
  postV1AppletsAppletIdComponent,
  putV1AppletsAppletIdStatus,
  getV1AppletsAppletIdComponentIdProfile,
  postV1AppletsAppletIdComponentIdProfile,
  postV2AppletsAppletIdComponents,
  getV1AuthorizationClearDirty,
  getV1AuthorizationMyPage,
  getV1AuthorizationMyTopicIdSet,
  getV1AuthorizationMyTopicCatalogIdLayers,
  getV1AuthorizationMyAppletIdComponent,
  getV1AuthorizationMyDatasetIdOrCodeTopic,
  getV1AuthorizationPage,
  getV1AuthorizationRoleset,
  getV1AuthorizationTopicRoleset,
  getV1AuthorizationAppletIdComponent,
  postV1AuthorizationRoleIdComponent,
  getV1AuthorizationRoleIdComponentPermission,
  postV1AuthorizationRoleIdPage,
  getV1AuthorizationRoleIdPagePermission,
  getV1AuthorizationRoleIdTopicDatasetIdPermission,
  postV1AuthorizationRoleIdDatasetIdTopic,
  getV1Datastandard,
  postV1Datastandard,
  deleteV1Datastandard,
  getV1DatastandardExport,
  getV1DatastandardField,
  postV1DatastandardField,
  deleteV1DatastandardField,
  postV1DatastandardFieldPage,
  getV1DatastandardFieldSort,
  postV1DatastandardFieldSort,
  postV1DatastandardImport,
  postV1DatastandardPage,
  getV1Datasets,
  putV1Datasets,
  postV1Datasets,
  deleteV1Datasets,
  postV1DatasetsBatchTopicLayers,
  postV1DatasetsBatchTopicLayersByCatalog,
  postV1DatasetsBatchTopicLayersByDataset,
  deleteV1DatasetsClearDirty,
  postV1DatasetsCopyTo,
  putV1DatasetsDir,
  getV1DatasetsExportDatasetId,
  getV1DatasetsFileDownload,
  postV1DatasetsImport,
  postV1DatasetsMoveTo,
  postV1DatasetsMoveUpdown,
  putV1DatasetsRefreshAddress,
  deleteV1DatasetsRemove,
  putV1DatasetsRepairTopicServiceid,
  putV1DatasetsSyncTopicAddress,
  postV1DatasetsSyncTopicsByDataset,
  putV1DatasetsTopic,
  deleteV1DatasetsCatalogId,
  postV1DatasetsDatasetIdOrUniqueCodeContentBuild,
  getV1DatasetsDatasetIdContent,
  postV1DatasetsDatasetIdDir,
  getV1DatasetsDatasetIdSearch,
  getV1DatasetsDatasetIdSubcatalog,
  postV1DatasetsDatasetIdTopic,
  deleteV1DatasetsTopicIdCoverClear,
  getV1DatasetsTopicIdLayers,
  putV1DatasetsTopicIdLayers,
  postV1DatasetsTopicIdLayers,
  getV1DatasetsTopicIdLayersClear,
  putV2DatasetsTopic,
  postV2DatasetsDatasetIdTopic,
  putV1Datasource,
  postV1Datasource,
  deleteV1Datasource,
  getV1DatasourcePageIndexSize,
  postV1DatasourceSchema,
  getV1DatasourceType,
  postV1DatasourceValid,
  getV1DatasourceId,
  getV1DatasourceIdSdeTable,
  getV1DatasourceIdSdeTableColumn,
  getV1DatasourceIdTable,
  getV1DatasourceIdTableMeta,
  getV1Dicts,
  putV1Dicts,
  postV1Dicts,
  deleteV1Dicts,
  getV1DictsByidDictId,
  getV1DictsCodeDictCodeItems,
  getV1DictsExport,
  postV1DictsImport,
  putV1DictsItem,
  postV1DictsItemRepair,
  postV1DictsItemUpdown,
  postV1DictsReplaceDictIdItems,
  getV1DictsType,
  putV1DictsType,
  postV1DictsType,
  deleteV1DictsType,
  postV1DictsUpdown,
  getV1DictsDictCode,
  getV1DictsDictIdItem,
  getV1DictsDictIdItems,
  postV1DictsDictIdItems,
  deleteV1DictsDictIdItems,
  getV2DictsCodeDictCodeItems,
  getV2DictsCodeDictCodeDicCodeItems,
  putV2DictsItem,
  postV2DictsItem,
  deleteV2DictsItems,
  getV2DictsItemsDicItemId,
  getV2DictsDictIdItems,
  getV1LogsExp,
  postV1Metadata,
  deleteV1Metadata,
  getV1MetadataDatatype,
  getV1MetadataElement,
  postV1MetadataElement,
  deleteV1MetadataElement,
  getV1MetadataList,
  getV1MetadataModel,
  postV1MetadataModel,
  deleteV1MetadataModel,
  putV1MetadataModelDataSort,
  putV1MetadataModelLink,
  postV1MetadataModelLink,
  deleteV1MetadataModelLink,
  postV1MetadataPage,
  postV1MetadataSystem,
  getV1MetadataSystemExport,
  postV1MetadataSystemImport,
  getV1MetadataSystemSystemDirIdStandards,
  getV1MetadataTree,
  getV1MetadataType,
  postV1MetadataType,
  deleteV1MetadataType,
  putV1MetadataTypeSwap,
  putV1MetadataValue,
  deleteV1MetadataValue,
  getV1MetadataValueCheck,
  getV1MetadataValueExport,
  getV1MetadataValueFile,
  deleteV1MetadataValueFile,
  postV1MetadataValueList,
  putV1MetadataValueShapeCount,
  postV1MetadataValueUpload,
  getV1UiResourcesComponent,
  postV1UiResourcesComponent,
  postV1UiResourcesComponentCopy,
  getV1UiResourcesComponentTree,
  postV1UiResourcesComponentComponentIdOperation,
  getV1UiResourcesDetailResourceId,
  postV1UiResourcesExport,
  postV1UiResourcesImport,
  postV1UiResourcesMoveTo,
  postV1UiResourcesMoveUpdown,
  getV1UiResourcesPage,
  postV1UiResourcesPage,
  getV1UiResourcesPageTree,
  postV1UiResourcesPagePageIdOperation,
  getV1UiResourcesSearchComponent,
  getV1UiResourcesSearchPage,
  deleteV1UiResourcesResourceId,
  getV1UcScenes,
  postV1UcScenes,
  deleteV1UcScenes,
  getV1UcTopics,
  postV1UcTopics,
  deleteV1UcTopics,
  postV1UcUiResources,
  getV1UcUiResourcesAppletId,
  deleteV1UcUiResourcesAppletId,
  getV1Zonings,
  putV1Zonings,
  postV1Zonings,
  deleteV1Zonings,
  putV1ZoningsDefaultVersionId,
  getV1ZoningsLevel,
  getV1ZoningsList,
  getV1ZoningsMyTree,
  getV1ZoningsSubtreeParentZoningCode,
  getV1ZoningsTree,
  postV1ZoningsUpdown,
  putV1ZoningsVersion,
  postV1ZoningsVersion,
  postV1ZoningsVersionCopyVersionId,
  getV1ZoningsVersions,
  getV2ZoningsListVersionId,
  getV2ZoningsSearchbyscope,
  getV2ZoningsSearchbyversion,
  getV2ZoningsSubtreeParentZoningCode,
  getV2ZoningsSubtreeVersionIdParentZoningCode,
  getV2ZoningsTreeVersionId,
  putV2ZoningsVersionId,
  postV2ZoningsVersionId,
  deleteV2ZoningsVersionId
};
