<template>
  <AConfigProvider :theme="themeData.antdv" :locale="zhCN" :get-popup-container="getPopupContainer">
    <AExtractStyle>
      <AStyleProvider
        :hash-priority="stylePriority"
        :transformers="[px2remTransformer(px2remOptions)]"
      >
        <div :id="appId" :style="themeData.css" :class="className ? className : 'w-full h-full bg text-content'">
          <slot />
        </div>
      </AStyleProvider>
    </AExtractStyle>
  </AConfigProvider>
</template>

<script lang="ts" setup>
  import type { ThemeConfig } from 'ant-design-vue/es/config-provider/context';
  import type { GlobalToken, MapToken } from 'ant-design-vue/es/theme/interface';
  import type { PropType } from 'vue';
  import type { ITheme } from '../composables/config';
  import { theme as antdvTheme, px2remTransformer } from 'ant-design-vue';
  import zhCN from 'ant-design-vue/es/locale/zh_CN';
  import defaultMap from 'ant-design-vue/es/theme/themes/default';
  import seed from 'ant-design-vue/es/theme/themes/seed';
  import formatToken from 'ant-design-vue/es/theme/util/alias';
  import dayjs from 'dayjs';
  import lodash from 'lodash';
  import { useRuntimeConfig } from 'nuxt/app';
  import { ref, watch } from 'vue';
  import { useDesignToken, useThemeVar } from '../composables/theme';
  import 'dayjs/locale/zh-cn';

  const props = defineProps({
    theme: {
      type: Object as PropType<ITheme>,
      default: () => undefined
    },
    stylePriority: {
      type: String,
      default: 'high'
    },
    className: {
      type: String
    }
  });

  dayjs.locale('zh-cn');

  const { defaultAlgorithm, darkAlgorithm, compactAlgorithm, defaultSeed } = antdvTheme;

  interface IThemeData {
    antdv?: any
    css?: string
    antdvVar?: any
    cssVar?: any
  }

  const themeVar = useThemeVar();
  const designToken = useDesignToken();
  const runtimeConfig = useRuntimeConfig();
  const themeData = ref<IThemeData>({});

  const appId: string = (runtimeConfig.public.appId || 'app') as string;

  const defaultTheme: ITheme = {
    algorithm: 'default',
    token: {
      colorPrimary: '#216BFF',
      colorInfo: '#216BFF',
      colorSuccess: '#03CC8F',
      colorWarning: '#EA6500',
      colorError: '#F20B00',
      colorBgBase: '#ffffff',
      colorTextBase: '#4E5969',
      fontSize: 16
    }
  };

  const px2remOptions: any = { rootValue: 20, precision: 2, mediaQuery: false };

  function getPopupContainer(triggerNode: any) {
    if (triggerNode)
      return triggerNode.parentNode;

    return document.getElementById(appId);
  }

  const algorithmMap: any = {
    default: defaultAlgorithm,
    dark: darkAlgorithm,
    compact: compactAlgorithm
  };

  function getDesignToken(config: ThemeConfig = {}): GlobalToken {
    const seedToken = { ...seed, ...config.token };
    const mapFn = config.algorithm ?? defaultMap;
    const mapToken = Array.isArray(mapFn)
      ? mapFn.reduce<MapToken>((result, fn) => fn(seedToken, result), undefined as any)
      : mapFn(seedToken);
    const mergedMapToken = {
      ...mapToken,
      ...config.components,
      override: config.token ?? {}
    };
    return formatToken(mergedMapToken);
  }

  function formatColor(hex: string) {
    if (!hex || hex.length > 7)
      return hex;

    // 去除可能存在的 # 符号
    const tempHex = hex.replace('#', '');

    // 将16进制颜色值分割成R、G、B三个部分
    const r = Number.parseInt(tempHex.substring(0, 2), 16);
    const g = Number.parseInt(tempHex.substring(2, 4), 16);
    const b = Number.parseInt(tempHex.substring(4, 6), 16);

    return `${r},${g},${b}`;
  }

  function rgbaToHex(color: string) {
    const val: any = color
      .replace(/rgba?\(/, '')
      .replace(/\)/, '')
      .replace(/[\s+]/g, '')
      .split(',');
    const a = Number.parseFloat(val[3] || 1);
    const r = Math.floor(a * Number.parseInt(val[0]) + (1 - a) * 255);
    const g = Math.floor(a * Number.parseInt(val[1]) + (1 - a) * 255);
    const b = Math.floor(a * Number.parseInt(val[2]) + (1 - a) * 255);
    return `#${(`0${r.toString(16)}`).slice(-2)}${(`0${g.toString(16)}`).slice(-2)}${(`0${b.toString(16)}`).slice(-2)}`;
  }

  function hexToHexByAlpha(hex: string, a: number) {
    const tempHex = hex.replace('#', '');

    // 将16进制颜色值分割成R、G、B三个部分
    const r = Number.parseInt(tempHex.substring(0, 2), 16);
    const g = Number.parseInt(tempHex.substring(2, 4), 16);
    const b = Number.parseInt(tempHex.substring(4, 6), 16);

    const newR = Number.parseInt(((r - (1 - a) * 255) / a).toString());
    const newG = Number.parseInt(((g - (1 - a) * 255) / a).toString());
    const newB = Number.parseInt(((b - (1 - a) * 255) / a).toString());
    return `#${(`0${newR.toString(16)}`).slice(-2)}${(`0${newG.toString(16)}`).slice(-2)}${(`0${newB.toString(16)}`).slice(-2)}`;
  }

  /**
   * 验证字符串是否为有效的十六进制颜色码（格式：#RGB 或 #RRGGBB）。
   * @param str 待验证的字符串
   * @returns 是否符合格式
   */
  function isHex(str: string): boolean {
    // eslint-disable-next-line regexp/no-unused-capturing-group
    return /^#([0-9A-F]{3}){1,2}$/i.test(str);
  }

  function formatTheme(themeConfig: ITheme) {
    const { algorithm, token, components = {}, inherit = true } = lodash.cloneDeep(themeConfig);
    const algorithmFn: any = algorithmMap[algorithm || defaultTheme.algorithm];
    const sendToken = { ...defaultTheme.token, ...token };
    const aliasToken = getDesignToken({
      token: sendToken,
      components,
      algorithm: algorithmFn,
      inherit
    });

    const antdvVar: any = {
      primaryColor: aliasToken.colorPrimary,
      hoverColor: aliasToken.colorPrimaryHover,
      lightColor: aliasToken.colorPrimary,
      linkColor: aliasToken.colorLink,
      infoColor: aliasToken.colorInfo,
      successColor: aliasToken.colorSuccess,
      warningColor: aliasToken.colorWarning,
      errorColor: aliasToken.colorError,
      backgroundColorBase: aliasToken.colorBgBase,
      backgroundColorLayout: aliasToken.colorBgLayout,
      fontSizeBase: aliasToken.fontSize,
      headingColor: aliasToken.colorTextBase,
      textColor: isHex(aliasToken.colorText) ? aliasToken.colorText : rgbaToHex(aliasToken.colorText),
      textColorSecondary: isHex(aliasToken.colorTextSecondary) ? aliasToken.colorTextSecondary : rgbaToHex(aliasToken.colorTextSecondary),
      disabledColor: isHex(aliasToken.colorTextDisabled) ? aliasToken.colorTextDisabled : rgbaToHex(aliasToken.colorTextDisabled),
      borderRadiusBase: aliasToken.borderRadius,
      borderColorBase: aliasToken.colorBorder,
      borderColorSecondary: aliasToken.colorBorderSecondary,
      dividerColor: aliasToken.colorBorder,
      boxShadowBase: aliasToken.boxShadow,
      lineHeightBase: aliasToken.lineHeight
    };
    const cssVar: any = {
      '--primary-color': antdvVar.primaryColor,
      '--light-color': antdvVar.lightColor,
      '--link-color': antdvVar.linkColor,
      '--info-color': antdvVar.infoColor,
      '--success-color': antdvVar.successColor,
      '--warning-color': antdvVar.warningColor,
      '--error-color': antdvVar.errorColor,
      '--background-color': antdvVar.backgroundColorBase,
      '--background-layout-color': antdvVar.backgroundColorLayout,
      '--heading-color': antdvVar.headingColor,
      '--content-color': antdvVar.textColor,
      '--content-secondary-color': antdvVar.textColorSecondary,
      '--disabled-color': antdvVar.disabledColor,
      '--border-color': antdvVar.borderColorBase,
      '--border-color-secondary': antdvVar.borderColorSecondary,
      '--divider-color': antdvVar.dividerColor,
      '--box-shadow': antdvVar.boxShadowBase
    };

    let cssStyle = '';
    Object.keys(cssVar).forEach((key) => {
      cssStyle += `${key}: ${formatColor(cssVar[key])};`;
    });
    const themeVar: IThemeData = {
      antdv: {
        token: aliasToken,
        components,
        inherit
      },
      css: cssStyle,
      antdvVar,
      cssVar
    };
    return themeVar;
  }

  watch(
    () => props.theme as ITheme,
    (newVal: ITheme) => {
      const { antdv, css, antdvVar, cssVar } = formatTheme(newVal || defaultTheme);
      themeData.value = { antdv, css };
      themeVar.value = { antdvVar, cssVar };
      designToken.value = antdv.token;
    },
    { immediate: true }
  );
</script>

<style scoped></style>
