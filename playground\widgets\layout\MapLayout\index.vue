<!--
 * @Author: Hertz
 * @Date: 2024-03-27 15:55:29
 * @LastEditTime: 2024-05-21 16:33:00
 * @LastEditors: G<PERSON><PERSON>AO
 * @Description:
-->
<template>
  <div class="map-layout flex h-full relative">
    <!-- 界面左区域 -->
    <div id="mapLeft" class="flex" />
    <div class="flex flex-col relative flex-1">
      <!-- 界面上区域 -->
      <div id="mapTop" class="flex top-0 left-0 right-0" />
      <div class="flex-1 relative">
        <!-- 界面上区域浮动 -->
        <div id="mapToptFloat" class="absolute left-0 top-0 right-0 flex" />
        <!-- 界面下区域浮动 -->
        <div id="mapBottomFloat" class="absolute left-0 right-0 bottom-0 flex" />
        <!-- 界面左区域浮动 -->
        <div id="mapLeftFloat" class="absolute left-0 top-0 bottom-0 flex" />
        <!-- 界面右区域浮动 -->
        <div id="mapRightFloat" class="absolute right-0 top-0 bottom-0 flex" />

        <!-- 界面左上角区域 -->
        <div id="mapTopLeft" class="absolute flex gap-xl left-0 top-0" />
        <!-- 界面右上角区域 -->
        <div id="mapTopRight" class="absolute flex gap-xl top-0 right-0" />
        <!-- 界面左下角区域 -->
        <div id="mapBottomLeft" class="absolute flex flex-col gap-xl bottom-0 left-0" />
        <!-- 界面右下角区域 -->
        <div id="mapBottomRight" class="absolute flex flex-col gap-xl bottom-0 right-0" />
      </div>
      <!-- 界面下区域 -->
      <div id="mapBottom" class="flex bottom-0 left-0 right-0" />
    </div>
    <!-- 界面右区域 -->
    <div id="mapRight" class="flex" />
  </div>
</template>

<script lang="ts" setup>
  const attrs = useAttrs();
  const { openWidget } = useWidget();
  const { subwidgets } = attrs as any;

  async function loadWidget() {
    subwidgets
      && subwidgets.forEach((item: any) => {
        openWidget(item);
      });
  }

  onMounted(async () => {
    loadWidget();
  });
</script>

<style lang="scss">
.map-tool {
  #mapLeft {
    > div > div {
      @apply mr-xl;
    }
  }
  #mapRight {
    > div > div {
      @apply ml-xl;
    }
  }
}
</style>
