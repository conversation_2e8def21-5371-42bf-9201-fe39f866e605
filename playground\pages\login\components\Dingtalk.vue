<template>
  <div class="dingtalk">
    <div
      class="relative overflow-hidden border m-auto"
      :style="{ width: '200px', height: '200px' }"
    >
      <iframe
        class="w-full h-full absolute top-1/2 left-1/2 overflow-visible"
        :style="{ width: '400px', height: '400px', transform: 'translate(-50%, -47%)' }"
        title="dingtalk-scan"
        :src="scanUrl"
      />
    </div>
    <div class="text-4xl mt-4xl">
      请使用<span class="font-bold mx-sm">浙政钉</span>扫描二维码登录
    </div>
  </div>
</template>

<script lang="ts" setup>
  const props = defineProps({
    options: {
      type: Object,
      default: () => ({})
    }
  });
  const route = useRoute();
  const scanUrl = ref(props.options.scanUrl);

  async function handleScanLogin(code: string) {
    try {
      const res = await collieApi.getV1DingtalkAuth({ code });
      console.log(res, '浙政钉扫码登录');
      if (res) {
        const { redirect } = route.query;
        const originUrl = (redirect || window.location.origin) as string;
        navigateTo(originUrl, { external: true });
      }
    }
    catch (error: any) {
      message.error(error.data.message || '当前用户无权限登录');
    }
  }

  function dingTalkScan(event: any) {
    if (event.data?.code)
      handleScanLogin(event.data?.code);
  }

  onMounted(() => {
    window.addEventListener('message', dingTalkScan);
  });
  onUnmounted(() => {
    window.removeEventListener('message', dingTalkScan);
  });
</script>

<style lang="scss" scoped>
.dingtalk {
}
</style>
