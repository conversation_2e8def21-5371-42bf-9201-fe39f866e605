diff --git a/node_modules/@micro-zoe/micro-app/lib/index.esm.js b/node_modules/@micro-zoe/micro-app/lib/index.esm.js
index b85f918..b537c3f 100644
--- a/node_modules/@micro-zoe/micro-app/lib/index.esm.js
+++ b/node_modules/@micro-zoe/micro-app/lib/index.esm.js
@@ -5626,7 +5626,7 @@ const EXCLUDE_URL_PROTOCOLS = [
     'blob:'
 ];
 // 重写 Worker 构造函数的类型
-const originalWorker = window.Worker;
+const originalWorker = isBrowser ? window.Worker : null;
 function isSameOrigin(url) {
     try {
         // 检查 URL 是否与当前页面在同一个源
@@ -5667,7 +5667,7 @@ function urlFromScript(script) {
     return URL.createObjectURL(blob);
 }
 // @ts-ignore
-const WorkerProxy = new Proxy(originalWorker, {
+const WorkerProxy = isBrowser ? new Proxy(originalWorker, {
     construct(Target, args) {
         let [scriptURL, options] = args;
         options = options || {};
@@ -5689,7 +5689,7 @@ const WorkerProxy = new Proxy(originalWorker, {
             return new Target(scriptURL, options);
         }
     },
-});
+}) : null;
 
 /**
  * patch window of child app
