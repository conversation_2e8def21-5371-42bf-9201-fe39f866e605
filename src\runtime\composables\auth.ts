import { getRequestHeader } from 'h3';
import { useRequestEvent, useState } from 'nuxt/app';

export interface IAuth {
  site: object
  user: object
  token: string | undefined
  connId: string | undefined
}

export const useAuth = () => useState('auth', () => undefined as IAuth | undefined);

export function getCookies() {
  if (import.meta.server)
    return getRequestHeader(useRequestEvent()!, 'cookie');
  else
    return document.cookie;
}
