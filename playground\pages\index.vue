<!--
 * @Author: <PERSON><PERSON>
 * @Date: 2024-04-12 16:00:28
 * @LastEditTime: 2024-04-12 16:59:30
 * @LastEditors: Hertz
 * @Description:
-->
<template>
  <div />
</template>

<script lang="ts" setup>
  const { pageList } = (usePage().value || {}) as IPageData;
  const defaultPage
    = pageList?.find((page: IResource) => page.permCodes?.includes('default_open')) || pageList?.[0];
  if (defaultPage)
    await navigateTo(defaultPage.url);
</script>
