/*
 * @Author: <PERSON><PERSON>T<PERSON>
 * @Date: 2021-07-22 15:29:17
 * @LastEditors: G.TAO
 * @LastEditTime: 2024-05-08 16:22:02
 * @Description:
 */
const fs = require('node:fs');
const process = require('node:process');
const pkg = require('./package.json');

const argvs = process.argv.slice(2);
console.log('argvs: ', argvs);
const __ALL__ = argvs[0].includes('.');
const __LARGE__ = argvs.includes('l');
const __MIDDLE__ = argvs.includes('m');

if (__ALL__) {
  pkg.version = argvs[0];
}
else {
  const vs = pkg.version.split('.');

  if (__LARGE__) {
    vs[0]++;
    vs[1] = 0;
    vs[2] = 0;
  }
  else if (__MIDDLE__) {
    vs[1]++;
    vs[2] = 0;
  }
  else {
    vs[2]++;
  }
  pkg.version = vs.join('.');
}

fs.writeFileSync('./package.json', `${JSON.stringify(pkg, null, 2)}\n`, { encoding: 'utf8' });
console.log(`version: ${pkg.version}\n`);
