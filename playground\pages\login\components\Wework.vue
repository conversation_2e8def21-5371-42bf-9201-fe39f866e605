<!--
 * @Author: <PERSON><PERSON>
 * @Date: 2024-04-12 16:00:28
 * @LastEditTime: 2025-03-18 16:24:13
 * @LastEditors: <PERSON><PERSON><PERSON>AO
 * @Description:
-->
<!--
 * @Author: <PERSON>tz
 * @Date: 2024-04-12 16:00:28
 * @LastEditTime: 2024-04-15 13:02:01
 * @LastEditors: Hertz
 * @Description:
-->
<template>
  <div class="wework">
    <div
      id="wework_iframe"
      class="relative overflow-hidden border m-auto"
      :style="{ width: '200px', height: '200px' }"
    />
    <div class="text-4xl mt-4xl">
      请使用<span class="font-bold mx-sm">企业微信</span>扫描二维码登录
    </div>
  </div>
</template>

<script lang="ts" setup>
  import * as ww from '@wecom/jssdk';
  import { WWLoginRedirectType, WWLoginType } from '@wecom/jssdk';

  const props = defineProps({
    options: {
      type: Object,
      default: () => ({})
    }
  });
  const route = useRoute();

  let wwLoginIns: any;
  function createWWLoginPanel(appid: string, agentid: string, successFn: any, failFn: any) {
    if (wwLoginIns) {
      wwLoginIns.unmount();
      wwLoginIns = null;
    }
    wwLoginIns = ww.createWWLoginPanel({
      el: '#wework_iframe',
      params: {
        login_type: WWLoginType.corpApp,
        appid,
        agentid,
        redirect_uri: window.location.origin,
        state: 'loginState',
        redirect_type: WWLoginRedirectType.callback
      },
      onCheckWeComLogin({ isWeComLogin }) {
        console.log(`isWeComLogin:${isWeComLogin}`);
      },
      onLoginSuccess({ code }) {
        console.log({ code });
        successFn(code);
        wwLoginIns.unmount();
        wwLoginIns = null;
      },
      onLoginFail(err) {
        console.log(err);
        failFn(err);
        wwLoginIns.unmount();
        wwLoginIns = null;
      }
    });
    wwLoginIns.el.className = 'w-full h-full absolute top-1/2 left-1/2 overflow-visible';
    wwLoginIns.el.style.transform = 'translate(-50%, -50%)';
  }
  function handleQYWXLogin() {
    const success = async (code: string) => {
      try {
        const res: any = await collieApi.getV1WeworkAuth({ code });
        console.log(res, '企业微信扫码登录');
        if (res) {
          const { redirect } = route.query;
          const originUrl = (redirect || window.location.origin) as string;
          navigateTo(originUrl, { external: true });
        }
      }
      catch (error: any) {
        console.error(error);
        message.error(error?.data?.message || '企业微信登陆失败!');
      }
    };
    const fail = (errMsg: any) => {
      message.error(errMsg.errMsg);
    };
    const { appid, agentid } = props.options;
    createWWLoginPanel(appid, agentid, success, fail);
  }
  onMounted(() => {
    handleQYWXLogin();
  });
</script>

<style lang="scss" scoped>
.wework {
}
</style>
