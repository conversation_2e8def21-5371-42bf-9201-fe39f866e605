<template>
  <div v-if="appInfo" class="micro-app h-full w-full relative">
    <WujieVue
      v-if="appInfo.frame === 'wujie'"
      class="h-full w-full"
      :name="appInfo.name"
      :url="appInfo.url"
      sync
      :loading="loadingEl"
      :attrs="{ src: wujieIframeSrc }"
      :props="appInfo.data"
      v-bind="appInfo.options"
      :load-error="handleAppError"
    />
    <div v-else class="w-full h-full">
      <template v-for="appItem in microApps" :key="appItem.name">
        <micro-app
          v-if="appItem.name === appInfo.name"
          class="h-full w-full"
          iframe
          v-bind="appItem.options"
          :name="appItem.name"
          :url="appItem.url"
          :default-page="appInfo.route || ''"
          :data="appInfo.data"
          @mounted="hadnleAppMounted"
          @error="handleAppError"
          @aftershow="handleAppShow"
        />
      </template>
    </div>
    <ASpin
      v-if="appInfo?.frame !== 'wujie' && loading"
      class="loading pointer-events-auto"
      :spinning="true"
      size="large"
    />
    <ASpin ref="spinRef" :spinning="true" class="wujie-loading" size="large" />
  </div>
</template>

<script lang="ts" setup>
  import type { PropType } from 'vue';
  import type { IApp } from '../composables/config';
  import { message } from 'ant-design-vue';
  import { useNuxtApp, useRuntimeConfig } from 'nuxt/app';
  import { nextTick, onMounted, ref, watch } from 'vue';
  import { useConfig } from '../composables/config';
  import { usePage } from '../composables/page';
  import { getClientUrl } from '../composables/request';
  import { useTheme, useThemeVar } from '../composables/theme';

  const props = defineProps({
    app: {
      type: Object as PropType<IApp>,
      default: () => ({})
    }
  });

  const { $wujie, $microApp }: any = useNuxtApp();
  const themeVar = useThemeVar();
  const runtimeConfig = useRuntimeConfig();
  const pageData = usePage();
  const config = useConfig();

  const theme = useTheme();
  const appInfo = ref<IApp>();
  const loading = ref<boolean>(true);
  const spinRef = ref(null);
  const loadingEl = ref(document.createElement('div'));
  const microApps = ref<IApp[]>([]);
  const wujieIframeSrc = `${runtimeConfig.app.baseURL}app.html`;

  onMounted(() => {
    const spin: any = spinRef.value;
    loadingEl.value = spin?.$el;
  });

  function getMicroAppCurrentRoute(name: string, basePath: string | undefined) {
    let curRoute = $microApp.router.decode($microApp.router.current.get(name).fullPath).replace(basePath || '', '');
    curRoute = curRoute.startsWith('/') ? curRoute : `/${curRoute}`;
    return curRoute;
  }

  watch(
    () => theme.value,
    (newVal: string) => {
      if (appInfo.value?.frame === 'wujie') {
        $wujie?.bus.$emit('themeChange', newVal);
      }
      else {
        const { name } = appInfo.value || ({} as IApp);
        $microApp.setData(name, { theme: newVal });
      }
    }
  );

  watch(
    () => props.app,
    async (newVal: IApp) => {
      const { name, url, protocol, host, port, path, frame, route, options, coverTheme, params }
        = newVal || ({} as IApp);
      const baseUrl
        = url
          || `${protocol || window.location.protocol}//${host || window.location.hostname}:${port || window.location.port}${path || ''}`;
      const apiData = config.value?.api
        ? {
          ...config.value.api,
          clientUrl: getClientUrl(config.value.api.clientUrl),
          list: config.value.api.list?.map((item) => {
            return {
              ...item,
              clientUrl: getClientUrl(item.clientUrl)
            };
          })
        }
        : undefined;
      if (frame === 'wujie') {
        if (name === appInfo.value?.name && options.alive) {
          $wujie?.bus.$emit('routeChange', route || '');
          return;
        }
        appInfo.value = {
          ...newVal,
          url: `${baseUrl}${route || ''}`,
          data: { baseUrl, theme: theme.value, coverTheme, themeConfig: config.value?.theme, pageData: pageData.value, apiData, ...params }
        };
        if (options.alive)
          $wujie?.bus.$emit('routeChange', route || '/');
      }
      else {
        if (name === appInfo.value?.name && options?.['keep-alive']) {
          const curRoute = getMicroAppCurrentRoute(name, appInfo.value.path);
          if (curRoute !== route) {
            $microApp.router.push({ name, path: route || '/' });
            return;
          }
        }
        const microApp = microApps.value.find((item: IApp) => item.name === name);
        appInfo.value = {
          ...newVal,
          url: baseUrl,
          route: route || '',
          data: { baseUrl, theme: theme.value, coverTheme, themeConfig: config.value?.theme, pageData: pageData.value, apiData, ...params }
        };
        if (microApp) {
          if (!microApp.options?.['keep-alive']) {
            loading.value = true;
            // 删除旧的，重新创建
            microApps.value = microApps.value.filter((item: IApp) => item.name !== name);
            nextTick(() => {
              microApps.value.push(appInfo.value!);
            });
          }
        }
        else {
          microApps.value.push(appInfo.value);
        }
      }
    },
    { immediate: true, deep: true }
  );

  function hadnleAppMounted() {
    loading.value = false;
  }
  function handleAppError(error: any) {
    loading.value = false;
    message.error('App加载失败');
    console.log('App加载失败', error);
  }
  function handleAppShow() {
    const { name, route, path } = appInfo.value as IApp;
    const curRoute = getMicroAppCurrentRoute(name, path);
    if (curRoute !== route) {
      $microApp.router.push({ name, path: route || '/' });
    }
    loading.value = false;
  }
</script>

<style lang="scss" scoped>
.micro-app {
  .loading {
    @apply flex items-center justify-center absolute top-0 bottom-0 left-0 right-0 w-full h-full !important;
  }
  .wujie-loading {
    @apply absolute -top-5xl !important;
  }
}
</style>
